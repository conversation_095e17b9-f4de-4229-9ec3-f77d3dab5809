#!/bin/bash

# 批量部署脚本 - 在物理主机上运行

# 目标服务器列表（基于 Prometheus 配置）
SERVERS=(
    "***********"
    "***********"
    "***********"
    "************"
    "************"
    "************"
    "************"
    "************"
    "***********"
    "***********"
    "***********"
    "***********"
    "************"
)

# SSH 配置
SSH_USER="root"
SSH_PASSWORD="Krjx!@QWE"
SSH_OPTIONS="-o StrictHostKeyChecking=no -o ConnectTimeout=10"

echo "=== 开始批量部署监控代理到目标服务器 ==="
echo "目标服务器: ${SERVERS[*]}"
echo "SSH 用户: $SSH_USER"
echo "部署组件: Promtail + Node Exporter"
echo ""

# 检查 sshpass 工具
if ! command -v sshpass &> /dev/null; then
    echo "错误: sshpass 未安装，正在尝试安装..."
    if command -v yum &> /dev/null; then
        sudo yum install -y sshpass
    elif command -v apt-get &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y sshpass
    else
        echo "请手动安装 sshpass: yum install sshpass 或 apt-get install sshpass"
        exit 1
    fi
fi

# 检查必要目录
required_dirs=("promtail-agent" "node-exporter-agent")
for dir in "${required_dirs[@]}"; do
    if [[ ! -d "$dir" ]]; then
        echo "错误: $dir 目录不存在"
        echo "请确保在正确的目录下运行此脚本"
        exit 1
    fi
done

# 为每台服务器部署
for server in "${SERVERS[@]}"; do
    echo "=========================================="
    echo "部署到服务器: $server"
    echo "=========================================="
    
    # 测试 SSH 连接
    echo "测试 SSH 连接..."
    if ! sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "echo 'SSH连接正常'" 2>/dev/null; then
        echo "✗ 无法连接到 $server，跳过此服务器"
        echo "请检查:"
        echo "  1. SSH 服务是否运行"
        echo "  2. 用户名和密码是否正确"
        echo "  3. 网络连通性"
        continue
    fi
    
    echo "✓ SSH 连接正常"
    
    # 创建远程目录
    echo "创建远程目录..."
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "mkdir -p ~/monitoring-agents/{promtail,node-exporter}"

    # 复制 Promtail 文件
    echo "复制 Promtail 配置文件..."
    sshpass -p "$SSH_PASSWORD" scp $SSH_OPTIONS -r promtail-agent/* $SSH_USER@$server:~/monitoring-agents/promtail/

    # 复制 Node Exporter 文件
    echo "复制 Node Exporter 配置文件..."
    sshpass -p "$SSH_PASSWORD" scp $SSH_OPTIONS -r node-exporter-agent/* $SSH_USER@$server:~/monitoring-agents/node-exporter/

    if [[ $? -ne 0 ]]; then
        echo "✗ 文件复制失败，跳过 $server"
        continue
    fi

    echo "✓ 文件复制完成"

    # 确定 Node Exporter 端口
    if [[ $server =~ ^10\.248\.18\. ]]; then
        NODE_EXPORTER_PORT="9110"
        COMPOSE_FILE="docker-compose-9110.yml"
    else
        NODE_EXPORTER_PORT="9100"
        COMPOSE_FILE="docker-compose.yml"
    fi

    echo "使用端口 $NODE_EXPORTER_PORT 部署 Node Exporter"

    # 远程执行部署
    echo "执行远程部署..."
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server << EOF
        # 部署 Node Exporter
        echo "部署 Node Exporter (端口 $NODE_EXPORTER_PORT)..."
        cd ~/monitoring-agents/node-exporter
        chmod +x deploy-node-exporter.sh

        # 使用对应的 compose 文件
        if [[ -f "$COMPOSE_FILE" ]]; then
            cp "$COMPOSE_FILE" docker-compose.yml
        fi

        ./deploy-node-exporter.sh

        # 部署 Promtail
        echo "部署 Promtail..."
        cd ~/monitoring-agents/promtail
        chmod +x deploy-promtail.sh
        ./deploy-promtail.sh
EOF
    
    if [[ $? -eq 0 ]]; then
        echo "✓ $server 部署成功"
    else
        echo "✗ $server 部署失败"
    fi
    
    echo ""
done

echo "=========================================="
echo "批量部署完成"
echo "=========================================="

# 验证所有服务器的连接
echo "验证所有服务器的监控服务..."
for server in "${SERVERS[@]}"; do
    echo "检查 $server:"

    # 检查 Loki 连接
    echo -n "  Loki 连接: "
    if sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "curl -s --connect-timeout 5 http://************:3100/ready > /dev/null" 2>/dev/null; then
        echo "✓"
    else
        echo "✗"
    fi

    # 检查 Node Exporter (根据服务器确定端口)
    if [[ $server =~ ^10\.248\.18\. ]]; then
        NODE_PORT="9110"
    else
        NODE_PORT="9100"
    fi

    echo -n "  Node Exporter ($NODE_PORT): "
    if sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "curl -s --connect-timeout 5 http://localhost:$NODE_PORT/metrics > /dev/null" 2>/dev/null; then
        echo "✓"
    else
        echo "✗"
    fi

    # 检查 Promtail
    echo -n "  Promtail: "
    if sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "curl -s --connect-timeout 5 http://localhost:9080/ready > /dev/null" 2>/dev/null; then
        echo "✓"
    else
        echo "✗"
    fi
done

echo ""
echo "=========================================="
echo "部署总结:"
echo "=========================================="
echo "中心监控服务:"
echo "  Grafana: http://************:3000 (admin/1qaz!QAZ)"
echo "  Prometheus: http://************:9090"
echo "  AlertManager: http://************:9093"
echo "  Loki: http://************:3100"
echo ""
echo "监控功能:"
echo "  ✓ 系统指标监控 (CPU、内存、磁盘、网络)"
echo "  ✓ 容器日志收集"
echo "  ✓ 系统日志收集"
echo "  ✓ 告警通知"
echo ""
echo "下一步:"
echo "  1. 访问 Grafana 配置监控面板"
echo "  2. 配置 AlertManager 邮件通知"
echo "  3. 导入预制的监控面板"
echo "  4. 测试告警规则"
