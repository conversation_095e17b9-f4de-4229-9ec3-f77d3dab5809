# OLP系统部署文档（完整版）

## 一、系统架构概述

### 1.1 系统组件
- **前端服务**：3个前端应用（olp-portal、olp-portal-2、olp-admin）
- **后端服务**：10个微服务模块
- **中间件服务**：Nacos、MySQL、Redis、XXL-JOB、Kafka、MinIO

### 1.2 服务端口分配
| 服务名称 | 容器名称 | 外部端口 | 内部端口 | 服务类型 |
|---------|----------|----------|----------|----------|
| olp-portal | olp-portal_uat | 83 | 80 | 前端 |
| olp-portal-2 | olp-portal_uat-2 | 82 | 80 | 前端 |
| olp-admin | olp-admin_uat | 84 | 80 | 前端 |
| olp-gateway | olp-gateway_uat | 28080 | 48080 | 网关 |
| olp-system | olp-system_uat | 28081 | 48081 | 系统服务 |
| olp-infra | olp-infra_uat | 28082 | 48082 | 基础设施 |
| olp-bpm | olp-bpm_uat | 28083 | 48083 | 工作流 |
| olp-ai | olp-ai_uat | 28085 | 48085 | AI服务 |
| olp-live | olp-live_uat | 28086 | 48086 | 直播服务 |
| olp-edp | olp-edp_uat | 28087/19992 | 48087/9999 | EDP服务 |
| olp-academy | olp-academy_uat | 28088/19991 | 48088/9999 | 学院服务 |
| olp-assistant | olp-assistant_uat | 28089 | 48089 | 助手服务 |
| olp-learning | olp-learning_uat | 28090 | 48090 | 学习服务 |
| olp-adapter | olp-adapter_uat | 28099 | 48099 | 适配器服务 |

## 二、部署前准备

### 2.1 服务器环境要求
- **操作系统**：Ubuntu 18.04+ / CentOS 7+
- **Docker版本**：20.10+
- **Docker Compose版本**：1.29+
- **内存要求**：至少16GB
- **磁盘空间**：至少100GB

### 2.2 目录结构准备
```bash
# 创建部署目录
mkdir -p /home/<USER>/projects/olp/{resource,logs}
mkdir -p /home/<USER>/{olp-portal,olp-portal-2,olp-admin}

# 设置权限
chmod -R 755 /home/<USER>/projects/olp
chmod -R 755 /home/<USER>
```

### 2.3 依赖服务检查
确保以下中间件服务已正常运行：
- Nacos (************:8848)
- MySQL (************:15306)
- Redis (************:6379)
- XXL-JOB (************:8080)
- Kafka (************:9092)
- MinIO (************:9000)

## 三、后端服务部署

### 3.1 JAR文件准备
将以下JAR文件上传到 `/home/<USER>/projects/olp/resource/` 目录：

1. **网关服务**
   - olp-gateway.jar

2. **核心业务服务**
   - olp-module-system-biz.jar (系统管理)
   - olp-module-infra-biz.jar (基础设施)
   - olp-module-bpm-biz.jar (工作流)

3. **AI相关服务**
   - olp-module-ai-biz.jar (AI核心)
   - olp-module-assistant-biz.jar (AI助手)

4. **教育培训服务**
   - olp-module-academy-biz.jar (学院)
   - olp-module-learning-biz.jar (学习)
   - olp-module-live-biz.jar (直播)

5. **数据处理服务**
   - olp-module-edp-biz.jar (数据处理)
   - olp-module-adapter-biz.jar (适配器)

### 3.2 Docker Compose配置
将提供的docker-compose.yml文件放置到 `/home/<USER>/projects/olp/` 目录下。

### 3.3 特殊配置说明

#### 3.3.1 DNS配置
部分服务配置了自定义DNS：
```yaml
dns:
  - *******
  - *******
  - ************
  - ************
```

#### 3.3.2 主机映射
EDP和Assistant服务配置了特殊主机映射：
```yaml
extra_hosts:
  - "ragflow-rd.majnoon-ifms.com:************"
```

#### 3.3.3 文件挂载
Live服务需要额外的文件挂载：
```yaml
volumes:
  - /home/<USER>/projects/olp/resource/bigbluebutton:/home/<USER>/projects/olp/resource/bigbluebutton
```

### 3.4 服务启动
```bash
cd /home/<USER>/projects/olp
# 启动所有服务
docker-compose up -d

# 检查服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f
```

## 四、前端资源部署

### 4.1 部署路径
```
/home/<USER>/
├── olp-portal/     # 用户端前端
├── olp-portal-2/   # 用户端前端2
└── olp-admin/      # 管理端前端
```

### 4.2 部署步骤
```bash
# 1. 备份现有文件
cd /home/<USER>/
cp -r olp-portal olp-portal-backup-$(date +%Y%m%d_%H%M%S)
cp -r olp-portal-2 olp-portal-2-backup-$(date +%Y%m%d_%H%M%S)
cp -r olp-admin olp-admin-backup-$(date +%Y%m%d_%H%M%S)

# 2. 上传并解压新版本
unzip olp-portal.zip -d olp-portal-new
unzip olp-portal-2.zip -d olp-portal-2-new
unzip olp-admin.zip -d olp-admin-new

# 3. 替换文件
rm -rf olp-portal-old olp-portal-2-old olp-admin-old
mv olp-portal olp-portal-old
mv olp-portal-2 olp-portal-2-old
mv olp-admin olp-admin-old
mv olp-portal-new olp-portal
mv olp-portal-2-new olp-portal-2
mv olp-admin-new olp-admin

# 4. 设置权限
chmod -R 755 olp-portal olp-portal-2 olp-admin
chown -R nginx:nginx olp-portal olp-portal-2 olp-admin
```

## 五、中间件服务信息

| 服务名称 | 地址 | 端口 | 账号 | 密码 | 备注 |
|---------|------|------|------|------|------|
| Nacos | http://************:8848/nacos | 8848 | nacos | nacos123 | 配置中心 |
| MySQL | ************ | 15306 | root | MySecureRootPassword123 | 主数据库 |
| Redis | ************ | 6379 | - | redis123 | 缓存服务 |
| XXL-JOB | http://************:8080/xxl-job-admin | 8080 | admin | 123456 | 任务调度 |
| Kafka | ************:9092 | 9092 | - | - | 消息队列 |
| MinIO | http://************:9000 | 9000 | olpuser | olppass123 | 对象存储 |

## 六、服务访问地址

### 6.1 前端访问地址
- **用户端1**：http://服务器IP:83
- **用户端2**：http://服务器IP:82
- **管理端**：http://服务器IP:84

### 6.2 后端服务地址
- **网关服务**：http://服务器IP:28080
- **系统服务**：http://服务器IP:28081
- **基础设施**：http://服务器IP:28082
- **工作流服务**：http://服务器IP:28083
- **AI服务**：http://服务器IP:28085
- **直播服务**：http://服务器IP:28086
- **EDP服务**：http://服务器IP:28087
- **学院服务**：http://服务器IP:28088
- **助手服务**：http://服务器IP:28089
- **学习服务**：http://服务器IP:28090
- **适配器服务**：http://服务器IP:28099

## 七、运维管理

### 7.1 服务管理命令
```bash
cd /home/<USER>/projects/olp

# 查看服务状态
docker-compose ps

# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启所有服务
docker-compose restart

# 重启指定服务
docker-compose restart olp-gateway
docker-compose restart olp-ai
```

### 7.2 日志管理
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看指定服务日志
docker-compose logs -f olp-gateway
docker-compose logs -f olp-ai

# 查看最近100行日志
docker-compose logs --tail=100 olp-gateway
```

### 7.3 健康检查
```bash
# 检查服务端口
netstat -tlnp | grep -E "(28080|28081|28082|28083|28085|28086|28087|28088|28089|28090|28099)"

# 检查容器状态
docker ps | grep olp

# 检查资源使用情况
docker stats
```

## 八、故障排查

### 8.1 常见问题及解决方案

#### 8.1.1 服务启动失败
```bash
# 检查端口占用
netstat -tlnp | grep 28080

# 检查容器日志
docker-compose logs olp-gateway

# 检查JAR文件是否存在
ls -la /home/<USER>/projects/olp/resource/

# 重新拉取镜像
docker-compose pull
docker-compose up -d
```

#### 8.1.2 服务无法访问
```bash
# 检查防火墙设置
systemctl status firewalld
firewall-cmd --list-ports

# 检查Docker网络
docker network ls
docker network inspect olp_default

# 检查服务健康状态
curl -I http://localhost:28080/actuator/health
```

#### 8.1.3 内存不足问题
```bash
# 检查系统内存使用
free -h
top

# 检查Docker容器资源使用
docker stats

# 清理无用的Docker资源
docker system prune -f
docker image prune -f
```

### 8.2 性能监控

#### 8.2.1 系统资源监控
```bash
# CPU使用率
top -p $(docker inspect --format='{{.State.Pid}}' olp-gateway_uat)

# 内存使用情况
docker exec olp-gateway_uat free -h

# 磁盘使用情况
df -h
du -sh /home/<USER>/projects/olp/logs/*
```

#### 8.2.2 应用性能监控
```bash
# JVM内存使用情况
docker exec olp-gateway_uat jstat -gc 1

# 线程状态
docker exec olp-gateway_uat jstack 1

# 网络连接状态
docker exec olp-gateway_uat netstat -an | grep ESTABLISHED | wc -l
```

### 8.3 备份与恢复

#### 8.3.1 配置文件备份
```bash
# 备份Docker Compose配置
cp /home/<USER>/projects/olp/docker-compose.yml \
   /home/<USER>/projects/olp/docker-compose.yml.backup.$(date +%Y%m%d)

# 备份前端资源
tar -czf /backup/olp-web-$(date +%Y%m%d).tar.gz /home/<USER>/
```

#### 8.3.2 日志备份
```bash
# 压缩旧日志
cd /home/<USER>/projects/olp/logs
find . -name "*.log" -mtime +7 -exec gzip {} \;

# 清理超过30天的日志
find . -name "*.gz" -mtime +30 -delete
```

## 九、安全配置

### 9.1 网络安全
```bash
# 配置防火墙规则
firewall-cmd --permanent --add-port=82-84/tcp
firewall-cmd --permanent --add-port=28080-28099/tcp
firewall-cmd --permanent --add-port=19991-19992/tcp
firewall-cmd --reload
```

### 9.2 容器安全
```bash
# 定期更新基础镜像
docker-compose pull
docker-compose up -d

# 检查容器安全漏洞
docker scan harbor.credat.com/backend/credat-java:21-jre
```

## 十、升级部署流程

### 10.1 滚动升级步骤
```bash
# 1. 备份当前版本
cp -r /home/<USER>/projects/olp/resource /home/<USER>/projects/olp/resource.backup.$(date +%Y%m%d)

# 2. 上传新版本JAR文件
# 使用scp或其他方式上传新的JAR文件

# 3. 逐个重启服务（避免全部服务同时停止）
docker-compose restart olp-gateway
sleep 30
docker-compose restart olp-system
sleep 30
docker-compose restart olp-infra
# ... 依次重启其他服务

# 4. 验证服务状态
docker-compose ps
curl -I http://localhost:28080/actuator/health
```

### 10.2 回滚流程
```bash
# 1. 停止当前服务
docker-compose down

# 2. 恢复备份文件
rm -rf /home/<USER>/projects/olp/resource
mv /home/<USER>/projects/olp/resource.backup.$(date +%Y%m%d) /home/<USER>/projects/olp/resource

# 3. 重新启动服务
docker-compose up -d

# 4. 验证回滚结果
docker-compose ps
```

## 十一、Nacos配置管理

### 11.1 命名空间配置
- **命名空间ID**：`olp-cloud-v2-uat`
- **命名空间名称**：`OLP UAT环境`

### 11.2 主要配置文件
1. `datasource.yml` - 数据源配置
2. `redis.yml` - Redis配置
3. `kafka.yml` - Kafka配置
4. `ai-server.yml` - AI服务配置
5. `minio.yml` - MinIO配置
6. `ragflow.yml` - RAGFlow配置

### 11.3 配置更新流程
```bash
# 1. 登录Nacos控制台
# http://************:8848/nacos

# 2. 选择对应命名空间
# olp-cloud-v2-uat

# 3. 修改配置文件

# 4. 发布配置

# 5. 验证服务是否自动刷新配置
docker-compose logs -f olp-gateway | grep "config refresh"
```

## 十二、附录

### 12.1 相关文档链接
1. [Docker官方文档](https://docs.docker.com/)
2. [Docker Compose文档](https://docs.docker.com/compose/)
3. [Nacos官方文档](https://nacos.io/zh-cn/docs/quick-start.html)
4. [Spring Boot Actuator文档](https://docs.spring.io/spring-boot/docs/current/reference/html/actuator.html)

### 12.2 联系方式
- **技术支持**：OLP运维团队
- **紧急联系**：值班电话
- **邮箱支持**：<EMAIL>

### 12.3 变更记录
| 版本 | 日期 | 变更内容 | 变更人 |
|------|------|----------|--------|
| v1.0 | 2025-01-29 | 初始版本 | 运维团队 |
| v2.0 | 2025-01-29 | 增加完整服务配置和故障排查 | 运维团队 |

---

**文档版本**：v2.0
**更新时间**：2025-01-29
**维护人员**：OLP运维团队
