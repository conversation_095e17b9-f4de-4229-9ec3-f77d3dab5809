#!/bin/bash

# Loki 配置修复脚本

echo "=== 修复 Loki 配置 ==="

# 检查配置文件是否存在
if [[ ! -f "loki/loki-config.yml" ]]; then
    echo "✗ Loki 配置文件不存在"
    exit 1
fi

echo "✓ Loki 配置文件存在"

# 检查 Docker 状态
echo "检查 Docker 状态..."
if docker --version > /dev/null 2>&1; then
    echo "✓ Docker 可用"
    
    # 尝试重启 Loki 服务
    echo "重启 Loki 服务..."
    if docker-compose restart loki 2>/dev/null; then
        echo "✓ Loki 服务重启成功"
        
        # 等待服务启动
        echo "等待 Loki 服务启动..."
        sleep 15
        
        # 检查服务状态
        echo "检查 Loki 服务状态..."
        for i in {1..10}; do
            if curl -s http://10.248.17.10:3100/ready > /dev/null; then
                echo "✓ Loki 服务正常运行"
                break
            else
                echo "等待 Loki 启动... ($i/10)"
                sleep 3
            fi
        done
        
        # 检查日志中是否还有错误
        echo "检查 Loki 日志..."
        if docker-compose logs loki 2>/dev/null | grep -E "(stream_retention|query_scheduler|frontend.*not found)"; then
            echo "⚠ 日志中仍有配置相关错误"
            echo "显示最近的日志:"
            docker-compose logs --tail=10 loki
        else
            echo "✓ 没有发现配置错误"
        fi
        
    else
        echo "⚠ 无法重启 Loki 服务（可能是 Docker Desktop 未运行）"
    fi
else
    echo "⚠ Docker 不可用"
fi

# 显示配置信息
echo ""
echo "=== Loki 配置信息 ==="
echo "配置文件: loki/loki-config.yml"
echo "服务端口: 3100"
echo "数据保留: 7天 (168h)"
echo "存储方式: 本地文件系统"
echo ""
echo "服务访问地址: http://10.248.17.10:3100"
echo "健康检查: http://10.248.17.10:3100/ready"
echo ""
echo "手动启动命令:"
echo "  docker-compose up -d loki"
echo "  docker-compose restart loki"
echo ""
echo "常用检查命令:"
echo "  docker-compose logs loki"
echo "  curl http://10.248.17.10:3100/ready"
echo "  curl http://10.248.17.10:3100/metrics"

echo ""
echo "=== Loki 修复完成 ==="
