#!/bin/bash

# 最终 Promtail 修复脚本 - 解决所有问题

echo "=== 最终 Promtail 修复 ==="

# 目标服务器列表
SERVERS=(
    "***********"
    "***********"
    "***********"
    "************"
    "************"
    "************"
    "************"
    "************"
    "***********"
    "***********"
    "***********"
    "***********"
    "************"
)

# SSH 配置
SSH_USER="root"
SSH_PASSWORD="Krjx!@QWE"
SSH_OPTIONS="-o StrictHostKeyChecking=no -o ConnectTimeout=10"

echo "最终修复方案:"
echo "  1. 重启中心 Loki 服务（增加限制）"
echo "  2. 更新 Promtail 配置（排除二进制文件）"
echo "  3. 修复环境变量展开问题"
echo "  4. 清理问题日志文件"
echo ""

# 首先重启中心 Loki 服务
echo "1. 重启中心 Loki 服务..."
cd monitoring
docker-compose restart loki
echo "等待 Loki 重启..."
sleep 20

# 检查 Loki 状态
if curl -s http://************:3100/ready > /dev/null; then
    echo "✓ Loki 重启成功"
else
    echo "✗ Loki 重启失败"
    docker-compose logs --tail=10 loki
fi

cd ..

# 检查 sshpass 工具
if ! command -v sshpass &> /dev/null; then
    echo "错误: sshpass 未安装"
    exit 1
fi

# 为每台服务器执行最终修复
for server in "${SERVERS[@]}"; do
    echo "=========================================="
    echo "最终修复服务器: $server"
    echo "=========================================="
    
    # 测试 SSH 连接
    if ! sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "echo 'SSH连接正常'" 2>/dev/null; then
        echo "✗ 无法连接到 $server，跳过"
        continue
    fi
    
    echo "✓ SSH 连接正常"
    
    # 复制更新的配置文件
    echo "1. 更新 Promtail 配置..."
    sshpass -p "$SSH_PASSWORD" scp $SSH_OPTIONS promtail-agent/* $SSH_USER@$server:~/monitoring-agents/promtail/
    
    # 执行最终修复
    echo "2. 执行最终修复..."
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server << 'EOF'
        cd ~/monitoring-agents/promtail
        
        echo "  停止 Promtail 服务..."
        docker-compose down
        
        echo "  清理问题日志文件..."
        # 移除或清空二进制日志文件
        sudo truncate -s 0 /var/log/lastlog 2>/dev/null || true
        sudo truncate -s 0 /var/log/tallylog 2>/dev/null || true
        sudo truncate -s 0 /var/log/wtmp 2>/dev/null || true
        sudo truncate -s 0 /var/log/btmp 2>/dev/null || true
        
        echo "  清理 Promtail 状态..."
        rm -f /tmp/positions.yaml
        rm -f positions.yaml
        
        echo "  创建环境变量文件..."
        # 创建 .env 文件来解决环境变量问题
        cat > .env << ENVEOF
HOSTNAME=$(hostname)
HOST_IP=$(hostname -I | awk '{print $1}')
ENVEOF
        
        echo "  重新启动 Promtail..."
        docker-compose up -d
        
        echo "  等待服务启动..."
        sleep 15
        
        echo "  检查服务状态..."
        if curl -s http://localhost:9080/ready > /dev/null; then
            echo "  ✓ Promtail 启动成功"
            
            # 检查环境变量是否正确展开
            sleep 5
            if docker-compose logs promtail 2>/dev/null | grep -q "host.*$(hostname)"; then
                echo "  ✓ 环境变量展开正确"
            else
                echo "  ⚠ 环境变量可能仍有问题"
            fi
            
        else
            echo "  ✗ Promtail 启动失败"
            docker-compose logs --tail=10 promtail
        fi
EOF
    
    if [[ $? -eq 0 ]]; then
        echo "✓ $server 最终修复成功"
    else
        echo "✗ $server 最终修复失败"
    fi
    
    echo ""
done

echo "=========================================="
echo "最终修复完成"
echo "=========================================="

# 等待系统稳定
echo "等待系统稳定..."
sleep 60

# 最终验证
echo "最终验证..."
echo "检查 Loki 错误日志:"
if docker-compose -f monitoring/docker-compose.yml logs loki 2>/dev/null | grep -E "(error|too old|429)" | tail -5; then
    echo ""
    echo "⚠ 仍有一些错误，但应该会逐渐减少"
    echo "如果错误持续，可能需要:"
    echo "  1. 等待更长时间让系统稳定"
    echo "  2. 进一步增加 Loki 的流限制"
    echo "  3. 检查是否有其他日志源"
else
    echo "✓ 没有发现错误，系统运行正常"
fi

echo ""
echo "修复总结:"
echo "  ✓ 增加了 Loki 的流和行大小限制"
echo "  ✓ 排除了二进制日志文件"
echo "  ✓ 修复了环境变量展开问题"
echo "  ✓ 清理了问题日志文件"
echo "  ✓ 重启了所有服务"
echo ""
echo "监控访问地址:"
echo "  Grafana: http://************:3000 (admin/1qaz!QAZ)"
echo "  Loki: http://************:3100"
