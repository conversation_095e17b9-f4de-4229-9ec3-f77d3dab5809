#!/bin/bash

# 强力修复 Promtail 日志问题

echo "=== 强力修复 Promtail 日志问题 ==="

# 目标服务器列表
SERVERS=(
    "***********"
    "***********"
    "***********"
    "************"
    "************"
    "************"
    "************"
    "************"
    "***********"
    "***********"
    "***********"
    "***********"
    "************"
)

# SSH 配置
SSH_USER="root"
SSH_PASSWORD="Krjx!@QWE"
SSH_OPTIONS="-o StrictHostKeyChecking=no -o ConnectTimeout=10"

echo "强力修复方案:"
echo "  1. 更新 Promtail 配置（正确的时间过滤）"
echo "  2. 清理旧的日志文件（7天前的）"
echo "  3. 重置 Promtail 位置文件"
echo "  4. 重启服务"
echo ""

# 检查 sshpass 工具
if ! command -v sshpass &> /dev/null; then
    echo "错误: sshpass 未安装"
    exit 1
fi

# 为每台服务器执行强力修复
for server in "${SERVERS[@]}"; do
    echo "=========================================="
    echo "强力修复服务器: $server"
    echo "=========================================="
    
    # 测试 SSH 连接
    if ! sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "echo 'SSH连接正常'" 2>/dev/null; then
        echo "✗ 无法连接到 $server，跳过"
        continue
    fi
    
    echo "✓ SSH 连接正常"
    
    # 复制更新的配置文件
    echo "1. 更新 Promtail 配置..."
    sshpass -p "$SSH_PASSWORD" scp $SSH_OPTIONS promtail-agent/* $SSH_USER@$server:~/monitoring-agents/promtail/
    
    # 执行强力清理和修复
    echo "2. 执行强力清理..."
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server << 'EOF'
        cd ~/monitoring-agents/promtail
        
        echo "  停止 Promtail 服务..."
        docker-compose down
        
        echo "  清理旧的日志文件（7天前）..."
        # 清理系统日志中的旧条目（保留文件但清空内容）
        find /var/log -name "*.log" -type f -mtime +7 -exec truncate -s 0 {} \; 2>/dev/null || true
        
        # 特别处理 yum.log（这个文件经常有很旧的条目）
        if [[ -f /var/log/yum.log ]]; then
            echo "  清理 yum.log 中的旧条目..."
            # 只保留最近7天的条目
            awk -v cutoff="$(date -d '7 days ago' '+%b %d')" '
                $1 " " $2 >= cutoff {print}
            ' /var/log/yum.log > /tmp/yum.log.new && mv /tmp/yum.log.new /var/log/yum.log
        fi
        
        echo "  重置 Promtail 位置文件..."
        rm -f /tmp/positions.yaml
        rm -f positions.yaml
        
        echo "  设置环境变量..."
        export HOSTNAME=$(hostname)
        export HOST_IP=$(hostname -I | awk '{print $1}')
        
        echo "  重新启动 Promtail..."
        docker-compose up -d
        
        echo "  等待服务启动..."
        sleep 15
        
        echo "  检查服务状态..."
        if curl -s http://localhost:9080/ready > /dev/null; then
            echo "  ✓ Promtail 启动成功"
        else
            echo "  ✗ Promtail 启动失败"
            docker-compose logs --tail=10 promtail
        fi
        
        echo "  检查是否还有时间戳错误..."
        sleep 10
        if docker-compose logs promtail 2>/dev/null | grep -q "too old"; then
            echo "  ⚠ 仍有时间戳错误，可能需要更多时间"
        else
            echo "  ✓ 没有发现时间戳错误"
        fi
EOF
    
    if [[ $? -eq 0 ]]; then
        echo "✓ $server 强力修复成功"
    else
        echo "✗ $server 强力修复失败"
    fi
    
    echo ""
done

echo "=========================================="
echo "强力修复完成"
echo "=========================================="

# 等待一段时间让所有服务稳定
echo "等待服务稳定..."
sleep 60

# 检查中心 Loki 的状态
echo "检查中心 Loki 状态..."
if docker-compose -f monitoring/docker-compose.yml logs loki 2>/dev/null | grep -E "(too old|429)" | tail -5; then
    echo "⚠ Loki 仍有错误，可能需要更多时间让旧日志过期"
    echo ""
    echo "建议操作:"
    echo "  1. 等待30分钟让系统稳定"
    echo "  2. 如果问题持续，考虑增加 Loki 的流限制"
    echo "  3. 检查是否有其他服务器仍在发送旧日志"
else
    echo "✓ Loki 状态正常，没有发现错误"
fi

echo ""
echo "修复总结:"
echo "  ✓ 更新了所有服务器的 Promtail 配置"
echo "  ✓ 清理了7天前的旧日志文件"
echo "  ✓ 重置了 Promtail 位置文件"
echo "  ✓ 重启了所有 Promtail 服务"
echo "  ✓ 修复了环境变量展开问题"
