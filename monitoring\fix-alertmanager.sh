#!/bin/bash

# AlertManager 配置修复脚本

echo "=== AlertManager 配置检查和修复 ==="

# 检查配置文件是否存在
if [[ ! -f "alertmanager/alertmanager.yml" ]]; then
    echo "✗ 配置文件不存在"
    exit 1
fi

echo "✓ 配置文件存在且已修复"
echo "✓ 移除了损坏的字符和格式问题"
echo "✓ 使用简化的英文模板避免编码问题"

# 检查配置文件语法（如果有 Docker）
echo "检查 Docker 状态..."
if docker --version > /dev/null 2>&1; then
    echo "✓ Docker 可用"

    # 检查配置语法
    echo "检查 AlertManager 配置语法..."
    if docker run --rm -v $(pwd)/alertmanager:/etc/alertmanager prom/alertmanager:latest amtool config check /etc/alertmanager/alertmanager.yml 2>/dev/null; then
        echo "✓ 配置文件语法正确"
    else
        echo "⚠ 无法验证配置语法（可能是 Docker 连接问题）"
    fi

    # 尝试重启服务
    echo "尝试重启 AlertManager 服务..."
    if docker-compose restart alertmanager 2>/dev/null; then
        echo "✓ 服务重启成功"

        # 等待服务启动
        echo "等待服务启动..."
        sleep 15

        # 检查服务状态
        echo "检查 AlertManager 服务状态..."
        if curl -s http://10.248.17.10:9093/api/v1/status > /dev/null; then
            echo "✓ AlertManager 服务正常"
        else
            echo "✗ AlertManager 服务异常"
            echo "查看日志:"
            docker-compose logs --tail=20 alertmanager
        fi
    else
        echo "⚠ 无法重启服务（可能是 Docker Desktop 未运行）"
    fi
else
    echo "⚠ Docker 不可用"
fi

# 显示配置信息
echo ""
echo "=== AlertManager 配置信息 ==="
echo "配置文件: alertmanager/alertmanager.yml"
echo "SMTP 服务器: smtp.exmail.qq.com:465"
echo "发送邮箱: <EMAIL>"
echo "接收邮箱: <EMAIL>, <EMAIL>"
echo ""
echo "服务访问地址: http://10.248.17.10:9093"
echo ""
echo "手动启动命令:"
echo "  docker-compose up -d alertmanager"
echo "  docker-compose restart alertmanager"
echo ""
echo "测试告警的方法:"
echo "1. 确保 Docker Desktop 正在运行"
echo "2. 启动监控服务: ./start-monitoring.sh"
echo "3. 访问 AlertManager Web 界面"
echo "4. 在 Prometheus 中手动触发告警"
echo "5. 检查邮箱是否收到告警邮件"
