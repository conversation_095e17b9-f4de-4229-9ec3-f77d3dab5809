auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096
  http_listen_address: 0.0.0.0
  grpc_listen_address: 0.0.0.0

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    instance_addr: ************
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

# 基础限制配置
limits_config:
  retention_period: 168h  # 7天保留
  ingestion_rate_mb: 64   # 增加摄入速率支持多服务器
  ingestion_burst_size_mb: 128
  max_query_series: 500000
  max_concurrent_tail_requests: 50
  max_streams_per_user: 20000
  max_line_size: 256000
  max_entries_limit_per_query: 10000

# 压缩和清理配置
compactor:
  working_directory: /loki/boltdb-shipper-compactor
  shared_store: filesystem
  compaction_interval: 10m
  retention_enabled: true
  retention_delete_delay: 2h
  retention_delete_worker_count: 150
