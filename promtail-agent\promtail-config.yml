server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://************:3100/loki/api/v1/push
    timeout: 30s
    backoff_config:
      min_period: 500ms
      max_period: 5m
      max_retries: 10
    # 批量发送配置
    batchwait: 1s
    batchsize: 1048576

scrape_configs:
  # Docker 容器日志收集
  - job_name: docker
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
        filters:
          - name: status
            values: ["running"]
    relabel_configs:
      # 保留必要的标签
      - target_label: 'job'
        replacement: 'docker'
      - target_label: 'environment'
        replacement: 'production'
      # 添加主机标识（简化版）
      - target_label: 'host'
        replacement: '${HOSTNAME}'
      # 过滤掉监控相关容器
      - source_labels: ['__meta_docker_container_name']
        regex: '.*(loki|grafana|promtail).*'
        action: drop
    pipeline_stages:
      # 尝试解析 JSON 格式日志
      - json:
          expressions:
            level: level
            message: message
            timestamp: timestamp
      # 时间戳处理
      - timestamp:
          source: timestamp
          format: RFC3339Nano
          location: "UTC"
          fallback_formats:
            - RFC3339
            - "2006-01-02T15:04:05.000Z"
            - "2006-01-02 15:04:05"
      # 严格的时间过滤
      - drop:
          expression: '.*'
          older_than: 24h  # 只保留24小时内的日志
      # 过滤未来时间戳
      - drop:
          expression: '.*'
          newer_than: 1h   # 过滤超过1小时的未来时间戳

  # 系统日志收集（确保有足够标签）
  - job_name: system
    static_configs:
      - targets:
          - localhost
        labels:
          job: system
          environment: production
          host: '${HOSTNAME}'
          __path__: /var/log/{messages,secure,cron,maillog,yum.log}
    pipeline_stages:
      # 解析系统日志格式
      - regex:
          expression: '^(?P<timestamp>\w+\s+\d+\s+\d+:\d+:\d+)\s+(?P<hostname>\S+)\s+(?P<service>\S+):\s*(?P<message>.*)'
      # 时间戳处理（添加年份）
      - timestamp:
          source: timestamp
          format: "Jan 02 15:04:05"
          location: "UTC"
      # 严格的时间过滤
      - drop:
          expression: '.*'
          older_than: 24h  # 只保留24小时内的日志
      # 过滤未来时间戳
      - drop:
          expression: '.*'
          newer_than: 1h   # 过滤超过1小时的未来时间戳

  # Nginx 访问日志（如果存在）
  - job_name: nginx_access
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx_access
          host: '${HOSTNAME}'
          host_ip: '${HOST_IP}'
          __path__: /var/log/nginx/access.log
    pipeline_stages:
      # 解析 Nginx 访问日志
      - regex:
          expression: '^(?P<remote_addr>[\d\.]+) - (?P<remote_user>[^ ]*) \[(?P<time_local>[^\]]*)\] "(?P<method>[^ ]*) (?P<request>[^ ]*) (?P<protocol>[^ ]*)" (?P<status>[\d]+) (?P<body_bytes_sent>[\d]+) "(?P<http_referer>[^"]*)" "(?P<http_user_agent>[^"]*)"'
      # 时间戳处理
      - timestamp:
          source: time_local
          format: "02/Jan/2006:15:04:05 -0700"
      # 过滤掉7天前的 Nginx 日志（在时间戳解析后）
      - drop:
          expression: '.*'
          older_than: 168h
      # 添加标签
      - labels:
          method:
          status:

  # Nginx 错误日志（如果存在）
  - job_name: nginx_error
    static_configs:
      - targets:
          - localhost
        labels:
          job: nginx_error
          host: '${HOSTNAME}'
          host_ip: '${HOST_IP}'
          level: ERROR
          __path__: /var/log/nginx/error.log

  # 应用日志目录（根据实际情况调整）
  - job_name: application
    static_configs:
      - targets:
          - localhost
        labels:
          job: application
          host: '${HOSTNAME}'
          host_ip: '${HOST_IP}'
          __path__: /var/log/app/*.log
    pipeline_stages:
      # 解析应用日志
      - json:
          expressions:
            level: level
            message: message
            timestamp: timestamp
      # 时间戳处理
      - timestamp:
          source: timestamp
          format: RFC3339Nano
      # 过滤掉7天前的应用日志（在时间戳解析后）
      - drop:
          expression: '.*'
          older_than: 168h
      # 添加标签
      - labels:
          level:
