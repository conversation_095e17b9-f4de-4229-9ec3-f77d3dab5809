服务器资源

序号
服务器
资源规格
部署
服务接口
账号
1
*************  2LPoC-AI-Toolkit
CPU:32V
RAM:176G
DISK:500G
ASR

http://*************:9002


2


ollama
http://*************:11434

3


libretranslate_live
http://*************:5000

4


portainer
http://*************:19000
admin/
admin/olp2#20240124
5
*************   2LPoC-OLP-LiveStream
CPU:20v
RAM:40G
DISK:500G
Live 
https://olp2-live-uat.majnoon-ifms.com/
Shared secret:

P11ESy1sFhjeACzmDynAQbSLCOWUaQ57E4ymYqxj
6
订阅GPU:
***********

-VCPU: 128 VCPU-RAM: 752Gib-GPU/ FPGA: 4*NVIDIA A10-GPU Memory: 4*24GB
file:asr
***********:9002




LLM(ollama)
***********:11434




机器学习翻译
***********:5000




直播:asr
***********:8765




portainer
***********:19000
admin/olp2#20240124

docker-compose 创建容器网络：olp2
networks:
  olp2:
    driver: bridge

docker-compose 配置网络使用方式
    networks:
      - olp2

基础环境安装
docker 安装
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

配置docker镜像存储目录
mkdir -p /home/<USER>
vim /etc/docker/daemon.json 
{
    "data-root": "/home/<USER>",
    "registry-mirrors": ["https://joi8b47k.mirror.aliyuncs.com"]
}
systemctl enable docker
systemctl daemon-reload
systemctl restart docker
systemctl status docker
#查看镜像存储的目录是否生效：预期显示Docker Root Dir: /home/<USER>
docker info |grep "Root Dir"


开启docker 远程portainer管理
vim /lib/systemd/system/docker.service

ExecStart=/usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock -H tcp://0.0.0.0:2375 

systemctl daemon-reload
systemctl restart docker


docker-compose 安装
#设置DNS,解决域名无法解析的问题
vim  /etc/resolv.conf
nameserver *******
nameserver ***************

sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

#解决主机名无法解析的问题
vim /etc/hosts
127.0.0.1 2LPoC-AI-Toolkit

sudo chmod +x /usr/local/bin/docker-compose
sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose


portainer安装
#创建数据外挂目录
mkdir -p /home/<USER>/data

docker pull portainer/portainer

docker run -p 19000:9000 --name portainer \
--restart=always \
-v /var/run/docker.sock:/var/run/docker.sock \
-v /home/<USER>/data:/data \
-d portainer/portainer


3. Install the NVIDIA Container Toolkit packages
curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey \
    | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list \
    | sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' \
    | sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
sudo apt-get update

sudo apt-get install -y nvidia-container-toolkit



Configure Docker to use Nvidia driver
sudo nvidia-ctk runtime configure --runtime=docker
sudo systemctl restart docker


应用部署
基础服务（CAT）
1. 保存镜像到本地
docker save -o /home/<USER>/cat/images/credat-cat-nginx.tar harbor.credat.com/frontend/credat-nginx:latest

docker save -o /home/<USER>/cat/images/credat-cat-java:21-jre.tar harbor.credat.com/backend/credat-java:21-jre

docker save -o /home/<USER>/cat/images/credat-cat-java:21-jre-tesseract.tar harbor.credat.com/backend/credat-java:21-jre-tesseract
2. 上传镜像到服务器 /home/<USER>/projects/cat/cat-images目录下
3. docker加载本地镜像tar包
docker load -i credat-cat-nginx.tar

docker load -i credat-cat-java_21-jre.tar

docker load -i credat-cat-java_21-jre-tesseract.tar
4. 上传jar包到服务器 /home/<USER>/projects/cat/resource目录下
5. 上传cat项目web端项目 /home/<USER>/projects/cat/resource目录下
6. 解压前端压缩包  
1. unzip cat-admin.zip
2. unzip cat-web.zip
7. 编写CAT项目docker-compse.yaml文件
version: '3.8'

services:
  cat-web:
    image: harbor.credat.com/frontend/credat-nginx:latest
    container_name: cat-web_uat
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
    ports: # 对外暴露的端口定义
      - 8081:80 # 访问nginx服务端口
    volumes:
      - ./logs:/root/logs/
      - ./cat-web:/usr/share/nginx/html/
    restart: always

  cat-admin:
    image: harbor.credat.com/frontend/credat-nginx:latest
    container_name: cat-admin_uat
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
    ports: # 对外暴露的端口定义
      - 8082:80 # 访问nginx服务端口
    volumes:
      - ./logs:/root/logs/
      - ./cat-admin:/usr/share/nginx/html/
    restart: always

  cat-gateway:
    restart: always
    container_name: cat-gateway_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "18080:48080"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./cat-gateway.jar:/jar/app.jar
    # network_mode: host
  cat-system:
    restart: always
    container_name: cat-system_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "18081:48081"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./cat-module-system-biz.jar:/jar/app.jar
    # network_mode: host
  cat-infra:
    restart: always
    container_name: cat-infra_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "18082:48082"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./cat-module-infra-biz.jar:/jar/app.jar
    # network_mode: host
  cat-aigc:
    restart: always
    container_name: cat-aigc_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "18092:48092"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./cat-module-aigc-biz.jar:/jar/app.jar
    # network_mode: host
  cat-qa:
    restart: always
    container_name: cat-qa_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "18091:48091"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./cat-module-qa-biz.jar:/jar/app.jar
    # network_mode: host
  cat-trans:
    restart: always
    container_name: cat-trans_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "18087:48087"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./cat-module-trans-biz.jar:/jar/app.jar
    # network_mode: host
  cat-voice:
    restart: always
    container_name: cat-voice_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "18088:48088"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./cat-module-voice-biz.jar:/jar/app.jar
    # network_mode: host
  cat-digit:
    restart: always
    container_name: cat-digit_uat
    image: harbor.credat.com/backend/credat-java:21-jre-tesseract
    ports:
      - "18089:48089"
    environment:
      - ACTIVE_PROFILE=uat
      - JAVA_OPTS=-Xms512m -Xmx2048m
    volumes:
      - ./logs:/root/logs/
      - ./cat-module-digit-biz.jar:/jar/app.jar
    # network_mode: host

8. 使用docker-compose启动服务
docker-compose up --force-recreate -d cat-gateway
docker-compose up --force-recreate -d cat-system
docker-compose up --force-recreate -d cat-infra
docker-compose up --force-recreate -d cat-aigc
docker-compose up --force-recreate -d cat-qa
docker-compose up --force-recreate -d cat-trans
docker-compose up --force-recreate -d cat-voice
docker-compose up --force-recreate -d cat-digit
AI服务
● asr:  用于音视频转写
● ollama：用于大模型翻译
● libtranslate：用于机器学习翻译

部署脚本
version: '3.5'

services: 
  asr:
    container_name: asr
    image: ghcr.io/speaches-ai/speaches:latest-cpu
    restart: unless-stopped
    ports:
      - 9002:8000
    environment:
      - WHISPER__MODEL=Systran/faster-whisper-large-v3
      - MAX_MODELS=4
    volumes:
      - /home/<USER>/projects/cat/model/asr:/home/<USER>/.cache/huggingface/hub
    healthcheck:
      test: ["CMD-SHELL", "curl http://localhost:9200"]
      interval: 10s
      timeout: 10s
      retries: 120
    networks:
      - olp2

  ollama:  
    container_name: ollama
    image: ollama/ollama:latest
    restart: unless-stopped
    ports:
      - "11434:11434"
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
      - OLLAMA_MODELS=/data/models
      - OLLAMA_NUM_PARALLEL=20
      - OLLAMA_MAX_LOADED_MODELS=3
      - OLLAMA_KEEP_ALIVE=30m
      - OLLAMA_SCHED_SPREAD=true
      - OMP_NUM_THREADS=128
      - MKL_NUM_THREADS=128
    volumes:
      - /home/<USER>/projects/cat/model/llm:/data/models
    healthcheck:
      test: ["CMD-SHELL", "curl http://localhost:11434"]
      interval: 10s
      timeout: 10s
      retries: 120
    networks:
      - olp2

  libretranslate:
    container_name: libretranslate_live
    image: libretranslate/libretranslate
    restart: unless-stopped
    ports:
      - "5000:5000"
    tty: true
    
    healthcheck:
      test: ['CMD-SHELL', './venv/bin/python scripts/healthcheck.py']     
    environment:
       - LT_UPDATE_MODELS=true
    volumes:
       - /home/<USER>/projects/cat/model/mlm:/home/<USER>/.local:rw
    networks:
      - olp2

networks:
  olp2:
    driver: bridge


模型数据外挂目录，数据外挂到宿主机
序号
服务
目录位置
模型
1
ASR
/home/<USER>/projects/cat/model/asr
models--Systran--faster-whisper-base/
models--Systran--faster-whisper-large-v2/
models--Systran--faster-whisper-large-v3/
models--Systran--faster-whisper-medium/
models--Systran--faster-whisper-small/
models--Systran--faster-whisper-tiny
2
ollama
/home/<USER>/data/models/
gemma3:27b
3
libtranslate
/home/<USER>/projects/cat/model/mlm
en,ar,zh
创建模型宿主机外挂目录
mkdir -p /home/<USER>/projects/cat/model/asr
mkdir -p /home/<USER>/projects/cat/model/llm
mkdir -p /home/<USER>/projects/cat/model/mlm

执行docker 脚本安装
cd /home/<USER>/projects/cat/ai

docker-compose -f docker-compose-ai.yml up -d

下载模型
# ASR 模型下载：
#使用 postman 调用接口 http://*************:9002/v1/audio/transcriptions
# 调用接口触发模型下载：
##分别设置model模型参数：
###1) Systran/faster-whisper-small
###2) Systran/faster-whisper-base
###3) Systran/faster-whisper-medium
###4) Systran/faster-whisper-large-v2
###5) Systran/faster-whisper-large-v3


#小米模型文件拷贝到ollama 外挂目录
cp Modelfile-gemmax2  gemmax2.gguf /home/<USER>/data/models/
#LLM ollama大模型下载
docker exec -it ollama /bin/bash
ollama pull gemma3:27b

#小米模型安装
ollama create gemmax2.gguf:latest   -f Modelfile-gemmax2 

#机器学习模型下载 容器已启动，模型自动下载，查看下载目录, 模型合计大小：9.2G
cd /home/<USER>/projects/cat/model/mlm/share/argos-translate/packages
ll


验证ai服务接口
使用postman 工具验证测试。

测试性能验证评估
用例号
服务
数据样本
模型参数
测试时长
1
ASR
时长：12分54秒
small
转写时长: 5分48秒

科睿GPU-A10:26秒
2
大模型翻译
947 长个字符
gemma2:2b
时长：15秒
应用服务
应用访问服务地址：http://*************:83/

3.3.1  应用Docker镜像
如未特别说明，OLP 应用服务的Docker镜像与CAT一致，具体步骤参考cat部署
3.3.2  docker compose 文件
将docker compose文件上传至/home/<USER>/projects/olp 目录下
version: '3.8'

services:
  olp-portal:
    image: harbor.credat.com/frontend/credat-nginx:latest
    container_name: olp-portal_uat
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
    ports: # 对外暴露的端口定义
      - 83:80 # 访问nginx服务端口
    volumes:
      - ./logs:/root/logs/
      - ./resource/olp-portal:/usr/share/nginx/html/
    restart: always
  #    network_mode: host # 以主机网络环境运行

  olp-admin:
    image: harbor.credat.com/frontend/credat-nginx:latest
    container_name: olp-admin_uat
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
    ports: # 对外暴露的端口定义
      - 84:80 # 访问nginx服务端口
    volumes:
      - ./logs:/root/logs/
      - ./resource/olp-admin:/usr/share/nginx/html/
    restart: always
  #    network_mode: host # 以主机网络环境运行

  olp-gateway:
    restart: always
    container_name: olp-gateway_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "28080:48080"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./resource/olp-gateway.jar:/jar/app.jar
  olp-system:
    restart: always
    container_name: olp-system_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "28081:48081"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./resource/olp-module-system-biz.jar:/jar/app.jar
  olp-infra:
    restart: always
    container_name: olp-infra_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "28082:48082"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./resource/olp-module-infra-biz.jar:/jar/app.jar
  olp-ai:
    restart: always
    container_name: olp-ai_uat
    image: harbor.credat.com/backend/credat-java:21-jre
    ports:
      - "28085:48085"
    environment:
      - ACTIVE_PROFILE=uat
    volumes:
      - ./logs:/root/logs/
      - ./resource/olp-module-ai-biz.jar:/jar/app.jar


3.3.3 版本部署

# 进入版本目录
cd /home/<USER>/projectes/olp/version

# 创建并进入对应版本目录,例如当前部署版本为v3.0
mkdir v3.0
cd v3.0/

# 使用rz命令上传当前版本变动的服务包或者前端包
rz

# 解压缩前端zip包之后删除,例如前端包为 olp-portal.zip
unzip olp-portal.zip
rm -f olp-portal.zip

# 将当前版本的服务包和前端包复制到启动目录
cp ./* /home/<USER>/projects/olp/resource

# 启动服务,可以全部重启或者指定服务启动
cd /home/<USER>/projects/olp
## 全部重启
docker-compose up --force-recreate -d 
## 指定服务重启,服务名称参考docker-compose.yml,例如重启或者启动 olp-ai服务
docker-compose up --force-recreate -d olp-ai

# 前端服务只需将资源目录复制过来就行,如不放心也可使用上面命令重启

3.3.4 执行sql
● 将更新的sql文件放在当前版本目录的/db目录下,在跳板机的navicat中执行。
初始化sql文件：
使用navicat登录OLP-UAT数据库，新建数据库olp-cloud-v2-uat，将初始化sql导入，之后清空以下表数据：
● infra_api_access_log
● infra_api_error_log
● infra_file
● infra_file_content
● system_oauth2_access_token
● system_oauth2_refresh_token
● system_operate_log
接着，修改infra_file_config中第三行minio的配置，内容为：
{
    "@class": "com.credat.olp.module.infra.framework.file.core.client.s3.S3FileClientConfig",
    "endpoint": "http://172.18.38.82:9000",
    "domain": "http://172.18.38.82:9000/olp-cloud-v2-uat",
    "bucket": "olp-cloud-v2-uat",
    "accessKey": "4KLUjWiOn9n7atI1",
    "accessSecret": "ZzeLMJDnvE64xcnIqgXyO61yT38bGe32"
}
3.3.5 Nacos配置
1. 登录UAT环境的nacos，打开olp-cloud-v2-uat的配置中心（如没有该命名空间请新建）
2. 导入nacos配置
3. 修改指定配置文件的uat相关配置，具体内容请根据uat服务器来配置：
‒ datasource.yml
‒ redis.yml
‒ rocketmq.yml
‒ ai-server.yml
3.3.6 Minio桶配置
登录minio管理页面，创建新Bocket： olp-cloud-v2-uat,并将桶的访问权限从private修改为public
数据初始化

CAT 部署

在线预览服务
加载本地镜像
docker load -i kkfileview1.0.1.tar
启动镜像 可选参数--restart
docker run -d -p 8012:8012 keking/kkfileview:1.0.1

直播系统部署
直播部署
1)源码下载
#1.下载源码
git clone -b bbb3.0  https://github.com/bigbluebutton/docker.git  bbb-docker-3.x-release

2）生成.env
#2. 编译脚本
cd bbb-docker-3.x-release
#3.生成环境变量配置:.env，选择安装的服务配置，都是y
./scripts/setup

vim .env
#4. 更新成最新的代码tag
vim ./script/collect-tags

#!/bin/bash
set -e
cd "$(dirname "$0")/.."
. scripts/functions.sh

# can't summarize the submodules without having the submodules
ensure_submodules

FILE=repos/tags
echo '# autogenerated by ./scripts/collect-tags' > $FILE
echo '#' >> $FILE
echo '# used to determine submodule tags without the need for' >> $FILE
echo '# checking out the whole submodule' >> $FILE
echo "" >> $FILE

# get list of submodules and their current tag as `git describe` also provides


git submodule foreach --quiet 'echo $sm_path $(git rev-list --tags --max-count=1)'  >> $FILE
3）#编译部署脚本的环境变量配置
vim .env
域名、ip、DEFAULT_LOCALE
IGNORE_TLS_CERT_ERRORS=true
# ====================================
# ADDITIONS to Live
# ====================================
# (place a '#' before to disable them)

# HTTPS Proxy
# fully automated Lets Encrypt certificates
ENABLE_HTTPS_PROXY=true
LETSENCRYPT_EMAIL=<EMAIL>

# Greenlight Frontend
# https://docs.bigbluebutton.org/greenlight/gl-overview.html
ENABLE_GREENLIGHT=true

# Enable Webhooks
# used by some integrations
ENABLE_WEBHOOKS=true

# Prometheus Exporter
# serves the bigbluebutton-exporter under following URL:
# https://yourdomain/bbb-exporter
ENABLE_PROMETHEUS_EXPORTER=true
ENABLE_PROMETHEUS_EXPORTER_OPTIMIZATION=true

# Recording
# IMPORTANT: this is currently a big privacy issues, because it will
# record everything which happens in the conference, even when the button
# suggets, that it does not.
# https://github.com/bigbluebutton/bigbluebutton/issues/9202
# make sure that you get peoples consent, before they join a room
ENABLE_RECORDING=true
REMOVE_OLD_RECORDING=false
#RECORDING_MAX_AGE_DAYS=14

# ====================================
# SECRETS
# ====================================
# important! change these to any random values
SHARED_SECRET=P11ESy1sFhjeACzmDynAQbSLCOWUaQ57E4ymYqxj
ETHERPAD_API_KEY=yv8HApdWdpVl7Y4jzfL34y4Y7U8nj1M1FFk1AhXr
RAILS_SECRET=a9f4686b177a6406cb803a20f62705d1cb6b8d2eabd824b14b5462954ab6740e33441546d36e448bd434343a21b182b05161f8f463ece25b640
POSTGRESQL_SECRET=6CfksvBeVc7JXRQ0XujAyMn0TUdsVlIMZqa1WY02
FSESL_PASSWORD=i9Dw2uu4xmxrItWRB4fcICO8pa9BWw9xyN6zTIE0
TURN_SECRET=9RUXaB7BJ5I47V4C6KCBT7MI3BSDB6B1

# ====================================
# CONNECTION
# ====================================

DOMAIN=olp2-live-uat.majnoon-ifms.com

EXTERNAL_IPv4=*************
EXTERNAL_IPv6=

STUN_IP=*************
STUN_PORT=3478

#TURN_EXT_SERVER=turns:example.org:443?transport=tcp
#TURN_EXT_SECRET=

# Allowed SIP IPs
# due to high traffic caused by bots, by default the SIP port is blocked.
# but you can allow access by your providers IP or IP ranges (comma seperated)
# Hint: if you want to allow requests from every IP, you can use 0.0.0.0/0
SIP_IP_ALLOWLIST=


# ====================================
# CUSTOMIZATION
# ====================================

# use following lines to replace the default welcome message and footer
WELCOME_MESSAGE="Welcome to <b>%%CONFNAME%%</b>!<br>To join the audio bridge click the speaker button.  Use a headset to avoid causing background noise for others."
WELCOME_FOOTER="This server is running"

# use following line for an additional SIP dial-in message

# for a different default presentation, place the pdf file in ./conf/ and
# adjust the following path
DEFAULT_PRESENTATION=./mod/nginx/default.pdf
DEFAULT_FAVICON=./mod/nginx/favicon.ico
# language of sound announcements
# options:
# - en-ca-june - EN Canadian June
# - en-us-allison - US English Allison
# - en-us-callie - US English Callie (default)
# - de-de-daedalus3 - German by Daedalus3 (https://github.com/Daedalus3/freeswitch-german-soundfiles)
# - es-ar-mario - Spanish/Argentina Mario
# - fr-ca-june - FR Canadian June
# - pt-br-karina - Brazilian Portuguese Karina
# - ru-RU-elena - RU Russian Elena
# - ru-RU-kirill - RU Russian Kirill
# - ru-RU-vika - RU Russian Viktoriya
# - sv-se-jakob - Swedish (Sweden) Jakob
# - zh-cn-sinmei - Chinese/China Sinmei
# - zh-hk-sinmei - Chinese/Hong Kong Sinmei
SOUNDS_LANGUAGE=en-us-callie

# set to true to disable announcements "You are now (un-)muted"
DISABLE_SOUND_MUTED=false

# set to true to disable announcement "You are the only person in this conference"
DISABLE_SOUND_ALONE=false

# set to false to disable the learning dashboard
ENABLE_LEARNING_DASHBOARD=true

IGNORE_TLS_CERT_ERRORS=true

TZ=Asia/Baghdad

# ====================================
# GREENLIGHT CONFIGURATION
# ====================================

### SMTP CONFIGURATION
# Emails are required for the basic features of Greenlight to function.
# Please refer to your SMTP provider to get the values for the variables below
#SMTP_SENDER_EMAIL=
#SMTP_SENDER_NAME=
#SMTP_SERVER=
#SMTP_PORT=
#SMTP_DOMAIN=demo.live.v3.olp.credat.com.cn
#SMTP_USERNAME=
#SMTP_PASSWORD=
#SMTP_AUTH=
#SMTP_STARTTLS_AUTO=true
#SMTP_STARTTLS=false
#SMTP_TLS=false
#SMTP_SSL_VERIFY=true

### EXTERNAL AUTHENTICATION METHODS
#
#OPENID_CONNECT_CLIENT_ID=
#OPENID_CONNECT_CLIENT_SECRET=
#OPENID_CONNECT_ISSUER=
#OPENID_CONNECT_REDIRECT=

# To enable hCaptcha on the user sign up and sign in, define these 2 keys
#HCAPTCHA_SITE_KEY=
#HCAPTCHA_SECRET_KEY=

# Set these if you are using a Simple Storage Service (S3)
# Uncomment S3_ENDPOINT only if you are using a S3 OTHER than Amazon Web Service (AWS) S3.
#S3_ACCESS_KEY_ID=
#S3_SECRET_ACCESS_KEY=
#S3_REGION=
#S3_BUCKET=
#S3_ENDPOINT=

# Define the default locale language code (i.e. 'en' for English) from the fallowing list:
#  [en, ar, fr, es]
DEFAULT_LOCALE=en

ENABLE_TRANSCRIPTION=false


4）生成docker-compose.yml

vim .env
IGNORE_TLS_CERT_ERRORS=false

vim /scripts/generate-compose
# https://hub.docker.com/r/bigbluebutton/bbb-build
BBB_BUILD_TAG=v3.0.x-release--2025-03-28-180244

./scripts/generate-compose

5) html5 client 端配置
repos/bigbluebutton/bigbluebutton-html5/private/config
vim repos/bigbluebutton/bigbluebutton-html5/private/config/settings.yml 
#1) 关闭登录后详情弹窗
:%s/showSessionDetailsOnJoin: true/showSessionDetailsOnJoin: false/
#2)修改客户端Title
:%s/clientTitle: BigBlueButton/clientTitle: OLP Live/
#3)修改应用名称
:%s/appName: BigBlueButton HTML5 Client/appName: Live HTML5 Client/
#4)修改帮助链接
:%s/helpLink: https:\/\/bigbluebutton\.org\/html5\//helpLink:
#5)修改copyright
:%s/copyright: '©2025 BigBlueButton Inc.'/copyright: '©2025 Credat Inc.'/

6)修改favicon.ico（未调试成功）
删除原来的：
rm -rf repos/bigbluebutton/bbb-learning-dashboard/public/favicon.ico repos/bigbluebutton/docs/static/img/favicon.ico repos/bigbluebutton/bbb-graphql-client-test/public/favicon.ico repos/bigbluebutton/bigbluebutton-config/assets/favicon.ico



cp favicon.ico  repos/bigbluebutton/bbb-learning-dashboard/public/ repos/bigbluebutton/docs/static/img/ repos/bigbluebutton/bbb-graphql-client-test/public/ repos/bigbluebutton/bigbluebutton-config/assets/

echo repos/bigbluebutton/bbb-learning-dashboard/public/  repos/bigbluebutton/docs/static/img/  repos/bigbluebutton/bbb-graphql-client-test/public/  repos/bigbluebutton/bigbluebutton-config/assets/ | xargs -I {} cp favicon.ico {}

#7)修改默认演示文稿
cp default.pdf  mod/nginx/

#8）关闭显示版本号

:%s/displayBbbServerVersion: true/displayBbbServerVersion: false/


6）mod/bbb-web/bbb-web.properties 修改

由于油田使用的内网域名，所以在.env 配置文件里的 IGNORE_TLS_CERT_ERRORS 设置成false。
# accept self signed certificates 
IGNORE_TLS_CERT_ERRORS=true
源文件：\mod\bbb-web\bbb-web.properties
{{ if isTrue .Env.IGNORE_TLS_CERT_ERRORS }}
beans.presentationService.defaultUploadedPresentation=https://test27.bigbluebutton.org/default.pdf
# fetch presentations without HTTPS
presentationBaseURL=http://{{ .Env.DOMAIN }}/bigbluebutton/presentation
{{else}}
beans.presentationService.defaultUploadedPresentation=${bigbluebutton.web.serverURL}/default.pdf
{{end}}
改成：

beans.presentationService.defaultUploadedPresentation=${bigbluebutton.web.serverURL}/default.pdf
7)  SSL证书文件生成与配置(haproxy)
文件地址：mod/haproxy
● 油田泛域名
主要使用了这两个文件
private key: majnoon-ifms.com.key
证书：majnoon-ifms.com.pem
● Dockerfile 脚本
FROM ghcr.io/tomdess/docker-haproxy-certbot:2.8.10
COPY majnoon-ifms.com*  /tmp
# overwrite bootstrap.sh
COPY bootstrap.sh /bootstrap.sh
● mod/haproxy/bootstrap.sh
#!/usr/bin/env bash

set -e

# save container environment variables to use it
# in cron scripts

declare -p | grep -Ev '^declare -[[:alpha:]]*r' > /container.env

if [ "$IGNORE_TLS_CERT_ERRORS"  ] && [ "$IGNORE_TLS_CERT_ERRORS" != "false" ]; then
    # use self signed certificate
    if [ ! -f /etc/haproxy/certs/haproxy-olp2-live-uat.majnoon-ifms.com.pem ]; then
        mkdir -p /etc/haproxy/certs
        # generate self signed certificate
      #  openssl req -x509 -nodes -days 700 -newkey rsa:2048 \
      #  -keyout /tmp/domain.key -out /tmp/domain.crt \
      #  -subj "/C=CA/ST=Quebec/L=Montreal/O=BigBlueButton Development/OU=bbb-docker/CN=olp2-live-uat.majnoon-ifms.com"

      #  cat /tmp/domain.key /tmp/domain.crt | tee /etc/haproxy/certs/haproxy-olp2-live-uat.majnoon-ifms.com.pem >/dev/null
      cat /tmp/majnoon-ifms.com.key /tmp/majnoon-ifms.com.pem   | tee  /etc/haproxy/certs/haproxy-olp2-live-uat.majnoon-ifms.com.pem >/dev/null
    fi
else
    # obtain certificates from lets encrypt
    /certs.sh
fi
supervisord -c /etc/supervisord.conf -n


7)  bigbluebutton/akka-bbb-apps
UpdateTranscriptPubMsgHdlr.scala 修改
文件位置：bigbluebutton\akka-bbb-apps\src\main\scala\org\bigbluebutton\core\apps\audiocaptions\UpdateTranscriptPubMsgHdlr.scala

1) 转写字幕改用消息体的msg.body.locale, 因为bbb-transcribe-controller 实现了中、阿、英三种语言的翻译， 所以用户在三种语言间进行订阅。 所以由用户的u.speechLocale 改为了msg.body.locale。

for {
        u <- Users2x.findWithIntId(liveMeeting.users2x, msg.header.userId)
      } yield {
        //msg.body.locale
        //CaptionDAO.insertOrUpdateCaption(msg.body.transcriptId, meetingId, msg.header.userId, transcript, c)
        CaptionDAO.insertOrUpdateCaption(msg.body.transcriptId, meetingId, msg.header.userId, transcript, msg.body.locale)
      }
8)  编译镜像
docker-compose build --parallel
9) 运行
docker-compose up -d

多语种字幕安装
bbb-transcription-controller
1）下载源码, 编译构建：
1)下载源码
git clone -b main  https://github.com/bigbluebutton/bbb-transcription-controller.git


2) 配置文件配置（default.yml）
bbb-transcription-controller\config\default.yml
● redis 配置、事件订阅通道: 开通端口6379（docker-compose.tmpl.yml）
● 转写provider配置，目前使用的是whisper
● freeswitch 通信连接配置：开通8021端口（docker-compose.tmpl.yml）
● 
log:
  filename: /var/log/bigbluebutton/bbb-transcription-controller.log
  level: debug
  stdout: true
  gladiaProxy: /var/log/bigbluebutton/gladia-proxy.log

redis:
  host: *************
  port: "6379"
  # password: foobared
  publishChannel: 'from-akka-apps-redis-channel'

includePartialResults: true

# If set to false connection to the transcription provider will be kept open from
# the call start to the call hangup. Otherwise it will be opened/closed whenever a
# user start/stops talking. Setting to true might yield lower quality results
disconnectOnSilence: false
# If disconnectOnSilence is true we'll wait this time in seconds
# before closing the connection. If this is too low we might have too much
# connections/disconnections and final results might be cut too short
closeConnectionAfterSeconds: 10

# in mhz
# Valid values are '16' and '8', but 8 seems to be
# too low to get accurate transcriptions
sampleRate: '16'

# Each transcription server has its own config section
# startMessage and endMessage are the messages sent whenever mod_audio_fork's
# connection starts/ends

# servers/server registers transcription server websocket URLs
# I've configured my servers using an nginx reverse proxy for each
# language, see the example below
vosk:
  startMessage: '{"config": { "sample_rate": "" } }'
  endMessage: '{"eof" : 1 }'
  servers:
    en-US:  ws://*************:2700/voskEN
    #en-US:  ws://*************:9200
    zh-CN:  ws://*************:2701/voskZH
    ar:  ws://*************:2702/voskAR
    #zh-CN: http://************:2701/voskZH
    #ar-ar: http://************:2702/voskAR
    #fr-FR: http://************:2700/voskFR

# TODO: whispering is still not supported, need to get
# the start/end messages right

gladia:
 # startMessage: '{"x_gladia_key": "1897c9b5-edce-4f7a-80bb-aef5708af287", "sample_rate": 0, "bit_depth": 16, "model_type": "fast", "endpointing": 300 }'
 # endMessage: '{}'
  # Provide a custom vocabulary to the model to improve accuracy of transcribing context specific words, technical terms, names, etc.
  # If empty, this field is ignored. Please be aware of a character limit of 600. 
#  hint: ''
#  server: http://************:7890
  proxy:
    enabled: false
 #   address: "wss://api.gladia.io/audio/text/audio-transcription"
 #   minPartialDuration: 3
 #   languageManual: true

whisper:
  startMessage: '{"type":"config","data":{"sampleRate":16000,"channels":2,"language":null,"processing_strategy":"silence_at_end_of_chunk","processing_args":{"chunk_length_seconds":1,"chunk_offset_seconds":0.1}}}'
  endMessage: '{"eof" : 1 }'
  server:  ws://***********:8765

# Setup freeswitch credentials to make ESL work

freeswitch:
  ip: *************
  port: 8021
  password: 'i9Dw2uu4xmxrItWRB4fcICO8pa9BWw9xyN6zTIE0'

# config/default.yml
translation:
  mode: LLM
  prompt:  'Translate this from {origin} to {target}:\n {origin}: {content}: \n {target}:'

  apiUrl: 'http://gpu.credat.com.cn/api/chat'
  #model: 'gemma2:9b-instruct-q8_0'
  model: 'gemmax2.gguf:latest'
  apiMLUrl: 'http://*************:5000/translate'

3) app.js 修改

修改备注说明：
新增了whiser provider
修改的方法：eslWrapper.onModAudioForkJSON，getProvider，bbbGW.on('UserSpeechLocaleChangedEvtMsg', startAudioFork
新增的方法：translateMultiLanguage、publishTranslation、translateLLM、translateML


'use strict'

const Logger = require('./lib/logger');

const config = require('config');
const { fork } = require('child_process');
const fs = require('fs');

const DISCONNECT_ON_SILENCE = config.disconnectOnSilence;
const CLOSE_CONNECTION_AFTER_SECONDS = config.closeConnectionAfterSeconds;

let GLADIA_PROXY_PROCESS;
const runGladiaProxy = () => {
  const outputFile = config.get('log.gladiaProxy');

  const outputStream = fs.createWriteStream(outputFile);

  outputStream.on('open', () => {
    // Spawn the child process
    GLADIA_PROXY_PROCESS = fork('gladia-proxy.js', [], {
      stdio: [null, outputStream, outputStream, 'ipc']
    });

    GLADIA_PROXY_PROCESS.on('exit', (code, signal) => {
      Logger.info(`Closing Gladia proxy code: ${code} signal: ${signal}`);
    });
  });

  Logger.info("Starting Gladia proxy");
}

if (config.get('gladia.proxy.enabled')) {
  runGladiaProxy();
}

const { tryParseJSON }  = require('./lib/utils');

const EventEmitter = require('events').EventEmitter;
const C = require('./lib/Constants');
const BigBlueButtonGW = require('./lib/bbb-gw');

const bbbGW = new BigBlueButtonGW();

const socketStatus = {};
const userChannels = {};
const stopTimeouts = {};

const REDIS_CHANNEL = config.get('redis.publishChannel')

bbbGW.addSubscribeChannel(REDIS_CHANNEL);
bbbGW.on('MeetingCreatedEvtMsg', (header, payload) => {
  setVoiceToMeeting(payload.props.voiceProp.voiceConf, payload.props.meetingProp.intId);
});

bbbGW.on('UserSpeechLocaleChangedEvtMsg', (header, payload) => {
  const { meetingId, userId } = header;
  const { provider, locale } = payload;

  if (!['gladia', 'vosk', 'whisper', ''].includes(provider)) {
    Logger.warn("Speech not changed, invalid provider " + userId + ' ' + provider + ' ' + locale);
    return;
  }

  Logger.info("Speech changed " + userId + ' ' + provider + ' ' + locale);

  setProvider(userId, provider, () => {
    setUserLocale(userId, locale, () => {

      let channelId = userChannels[userId];
      if (channelId && socketStatus[channelId]) {
        stopAudioFork(channelId);
        setTimeout(() => {
          startAudioFork(channelId, userId);
        }, 1000);
      }
    });
  });
});

bbbGW.on('UserSpeechOptionsChangedEvtMsg', (header, payload) => {
  const { meetingId, userId } = header;
  const { partialUtterances, minUtteranceLength } = payload;

  Logger.info("User speech options changed " + ' ' + meetingId + ' ' + userId + ' ' + partialUtterances + ' ' + minUtteranceLength);

  setUserPartialUtterance(userId, partialUtterances, () => {
    setUserMinUtteranceLength(userId, minUtteranceLength, () => {

      let channelId = userChannels[userId];
      if (channelId && socketStatus[channelId]) {
        stopAudioFork(channelId);
        setTimeout(() => {
          startAudioFork(channelId, userId);
        }, 1000);
      }

    });
  });
});

const REDIS_VOICE_ID_KEY = 'bbb-transcription-manager_voiceToMeeting';
const getVoiceToMeeting = (voiceConf, cb) => {
  bbbGW.getKey(REDIS_VOICE_ID_KEY + '_' + voiceConf, cb);
};

const setVoiceToMeeting = (voiceConf, meetingId, cb) => {
  bbbGW.setKey(REDIS_VOICE_ID_KEY + '_' + voiceConf, meetingId, cb);
};

const REDIS_USER_LOCALE_KEY = 'bbb-transcription-manager_locale';
const getUserLocale = (userId, cb) => {
  bbbGW.getKey(REDIS_USER_LOCALE_KEY + '_' + userId, cb);
};

const setUserLocale = (userId, locale, cb) => {
  bbbGW.setKey(REDIS_USER_LOCALE_KEY + '_' + userId, locale, cb);
};

const REDIS_USER_PARTIAL_UTTERANCE_KEY = 'bbb-transcription-manager_partial_utterance';
const getUserPartialUtterance = (userId, cb) => {
  bbbGW.getKey(REDIS_USER_PARTIAL_UTTERANCE_KEY + '_' + userId, cb);
};

const setUserPartialUtterance = (userId, partialUtterance, cb) => {
  bbbGW.setKey(REDIS_USER_PARTIAL_UTTERANCE_KEY + '_' + userId, partialUtterance, cb);
};

const REDIS_USER_MIN_UTTERANCE_LENGTH_KEY = 'bbb-transcription-manager_min_utterance_length';
const getUserMinUtteranceLength = (userId, cb) => {
  bbbGW.getKey(REDIS_USER_MIN_UTTERANCE_LENGTH_KEY + '_' + userId, cb);
};

const setUserMinUtteranceLength = (userId, minUtteranceLength, cb) => {
  bbbGW.setKey(REDIS_USER_MIN_UTTERANCE_LENGTH_KEY + '_' + userId, minUtteranceLength, cb);
};

const REDIS_TRANSCRIPTION_PROVIDER_KEY = 'bbb-transcription-manager_provider';
const getProvider = (userId, cb) => {
  bbbGW.getKey(REDIS_TRANSCRIPTION_PROVIDER_KEY + '_' + userId, cb);
};

const setProvider = (userId, provider, cb) => {
  bbbGW.setKey(REDIS_TRANSCRIPTION_PROVIDER_KEY + '_' + userId, provider, cb);
};

const EslWrapper = require('./lib/esl-wrapper');
const eslWrapper = new EslWrapper();

const SAMPLE_RATE = config.get("sampleRate");

const INCLUDE_PARTIAL_RESULTS = config.get("includePartialResults");

const getServerUrl = (userId, cb) => {

  getProvider(userId, (err, provider) => {
    Logger.info(`------------------provider:${provider}`);
    getUserLocale(userId, (err, locale) => {

      if (provider && provider != '' && locale && locale != '') {
       
        let serverUrl = '';

        if(provider === 'gladia' || provider === 'whisper'){
          serverUrl = config.get(provider+'.server' );
        }else {
          serverUrl = config.get(provider + '.servers.' + locale);
        }

        return cb(serverUrl, provider, locale);
      } else {
        return cb(null);
      }
    });
  });
};

const makeMessage = (meetingId, userId, locale, transcript, result, start = 0, end = 0) => {

  return {
    envelope: {
      name: 'UpdateTranscriptPubMsg',
      routing: {
        meetingId,
        userId,
      },
      timestamp: Date.now(),
    },
    core: {
      header: {
        name: 'UpdateTranscriptPubMsg',
        meetingId,
        userId,
      },
      body: {
        transcriptId: userId + '-'+ start,
        start: start.toString(),
        end: end.toString(),
        text: '',
        transcript: transcript ,
        locale: locale,
        result: result,
      },
    }
  };
};

const makeErrorMessage = (body, meetingId, userId) => {
  const name = 'TranscriptionProviderErrorMsg';
  return {
    envelope: {
      name,
      routing: {
        meetingId,
        userId,
      },
      timestamp: Date.now(),
    },
    core: {
      header: {
        name,
        meetingId,
        userId,
      },
      body: {
        errorCode: body.errorCode,
        errorMessage: body.errorMessage
      },
    }
  };
}


const startAudioFork = (channelId, userId) => {
  Logger.info(`Start mod_audio_fork connection ${channelId} ${userId}`);

  getServerUrl(userId, (serverUrl, provider, language) => {
    getUserPartialUtterance(userId, (err, partialUtterances) => {
      getUserMinUtteranceLength(userId, (err, minUtteranceLength) => {

        if (!serverUrl) {
          Logger.warn("No provider set, not transcribing");
          return;
        }

        const initialMessage = JSON.parse(config.get(provider + '.startMessage'));

        if (provider === 'vosk') {
          initialMessage.config.sample_rate = SAMPLE_RATE + '000';
        }

        if (provider === 'whisper') {
          initialMessage.data.sampleRate = SAMPLE_RATE + '000';
        }

        if (provider === 'gladia') {
          initialMessage.sample_rate = parseInt(SAMPLE_RATE + '000')
          initialMessage.language = language == 'auto' ? language : language.slice(0,2);
          initialMessage.partialUtterances = partialUtterances;
          initialMessage.minUtteranceLength = minUtteranceLength;
          initialMessage.transcription_hint = config.get(provider + '.hint');
        }

        if (!socketStatus[channelId]) {
          eslWrapper._executeCommand(`uuid_audio_fork ${channelId} start ${serverUrl} mono ${SAMPLE_RATE}k ${JSON.stringify(initialMessage)}`);
          socketStatus[channelId] = true;
          userChannels[userId] = channelId;
        }
      });
    });
  });
};

const stopAudioFork = (channelId) => {
  Logger.info(`Stop mod_audio_fork connection ${channelId}`);
  const endMessage = JSON.parse(config.get('vosk.endMessage'));

  if (socketStatus[channelId]) {
    try{
      eslWrapper._executeCommand(`uuid_audio_fork ${channelId} stop ${JSON.stringify(endMessage)}`);
    } catch (e) {
      Logger.error("Socket already closed");
    }
    socketStatus[channelId] = false;
  }
};
//------------------------------------------------------------------
const axios = require('axios');

async function translateMultiLanguage(fromLocale, payload, transcription) {
  //const locales = ["en-US","zh-CN","ar"];
  const locales = ["en-US","zh-CN","ar"];
  const mode = config.get('translation.mode');

  for (let i = 0; i < locales.length; i++) {
    if (locales[i] !== fromLocale) {
      let targetLocale = locales[i];
      //
      if(mode == "LLM") {
        // translatedText = translateTranscription(fromLocale, transcription);
          translateLLM(fromLocale, targetLocale, transcription)
            .then(content => {
              if(content){
                  publishTranslation(targetLocale, payload, content);
              }
            })
            .catch(error => {
              console.error('Error during translation:', error);
            });
       } else {
          translateML(targetLocale, transcription)
            .then(translatedText => {
              if(translatedText){
                publishTranslation(targetLocale, payload, translatedText);
              }
            })
            .catch(error => {
              console.error('Error during translation:', error);
            });
       } 

    }
  }
}



async function translateLLM(fromLocale, targetLocale, transcription) {
  try {
    let targetLanguage = null;

    // If the source language is Chinese or Arabic, set targetLocale to English
    if (targetLocale === 'zh-CN'){
      targetLanguage = 'Chinese Simplified';
    }else if(targetLocale === 'ar'){
      targetLanguage = 'Arabic';
    }else{
      targetLanguage = 'English';
    }

    let fromLanguage = null;
    if (fromLocale === 'zh-CN'){
      fromLanguage = 'Chinese Simplified';
    }else if(fromLocale === 'ar'){
      fromLanguage === 'Arabic';
    }else{
      fromLanguage === 'English';
    }
    //let systemMessage = config.get('translation.systemMessage');
    let user_prompt = config.get('translation.prompt');

    user_prompt = user_prompt.replaceAll('{origin}', fromLanguage);
    user_prompt = user_prompt.replaceAll('{target}', targetLanguage);
    user_prompt = user_prompt.replace('{content}', transcription);

    const apiUrl = config.get('translation.apiUrl');
    const model = config.get('translation.model');

    //Logger.info(`--------LLM--------request param------: ${user_prompt}`)

    const response = await axios.post(apiUrl, {
        model: model,
        stream: false,
        temperature: 0.2,
        top_p: 0.9,
        messages: [
          {
            role: "user",
            content: user_prompt
          }
        ]
    });


    const responseBody = response.data;
   // Logger.info(`--------LLM--------translated text: ${JSON.stringify(responseBody)}`)

    if (responseBody && responseBody.message.content) {
      
      return responseBody.message.content; // 返回翻译后的文本
    } else {
      Logger.error('Translation API did not return translated text');
      return null; // 或者返回一个默认值
    }

  } catch (error) {
    Logger.error(`Error LLM translating transcription:${error}`);
    return null;
  }
}


async function translateML(targetLocale, transcription) {
  

    const targetLanguage =  targetLocale.split('-')[0];;
    const apiUrl = config.get('translation.apiMLUrl');
    try {

    // Make sure to use a function that returns a promise for request
    const response = await axios.post(apiUrl, {
        q: transcription,
        source: "auto",
        target: targetLanguage
    });

    const responseBody = response.data;

    if (responseBody && responseBody.translatedText) {
      Logger.info(`----------------translated text: ${JSON.stringify(responseBody)}`)
      return responseBody.translatedText; // 返回翻译后的文本
    } else {
      Logger.error('Translation API did not return translated text');
      return null; // 或者返回一个默认值
    }

  } catch (error) {
    Logger.error(`Error translating transcription:${error}`);
    return null;
  }
}

async function  publishTranslation(targetLocale, payload, translatedText){
  //双语显示
  payload.core.body.transcriptId = payload.core.body.transcriptId + targetLocale;
  // Update the payload with the translated text
  payload.core.body.transcript= translatedText; 
  payload.core.body.locale=targetLocale;
  
  const translatedPayload = JSON.stringify(payload);
  Logger.info(`======================translate text: ${translatedPayload}`);
  // Publish the translated transcription
  bbbGW.publish(translatedPayload, C.TO_AKKA_APPS_CHAN_2x);
}



try {
  const translatedText = translateLLM('en-US','zh-CN', 'What are you doing?').then(body=>{
    Logger.info(`==================test===================Translated Text:${JSON.stringify(body)}`);
  });
  
} catch (error) {
  Logger.error(`Translation failed: ${error}`);
}


//------------------------------------------------------------------



eslWrapper.onModAudioForkJSON((msg, channelId, userId) => {

  getVoiceToMeeting(msg.getHeader('variable_conference_name'), (err, meetingId) => {
    getUserLocale(userId, (err, locale) => {
     
      
      const body = tryParseJSON(msg.body);
      const transcription = body.text || body.partial;

      if (body.errorCode) {
        Logger.error("Transcription error", body);
        const msg = makeErrorMessage(body, meetingId, userId);
        return bbbGW.publish(JSON.stringify(msg), C.TO_AKKA_APPS_CHAN_2x);
      }

      if (body.partial && !INCLUDE_PARTIAL_RESULTS) {
        Logger.debug('Discard partial utterance', body.partial);
        return;
      }

      const result = Boolean(body.text || body.partial);
      
      if(!result){
        return
      }
     
      const fromLocale = body.locale || locale;

      const payload = makeMessage(meetingId, userId, fromLocale, transcription, result, body.time_begin, body.time_end);
      const str_payload = JSON.stringify(payload);
      

        
      translateMultiLanguage(fromLocale, payload, transcription) 

      /*Logger.info(`transcription text is: ${str_payload}, ===============body:${JSON.stringify(body)},---------------msg:${JSON.stringify(msg)}`);*/
      Logger.info(`transcription text is: ${str_payload}`);
      bbbGW.publish(str_payload, C.TO_AKKA_APPS_CHAN_2x);
    });
  });
});

eslWrapper.onModAudioForkDisconnect((msg, channelId, userId) => {
  Logger.info(`mod_audio_fork connection dropped ${channelId} ${userId}`);
});

const handleChannelAnswer = (channelId, callId, userId) => {
  Logger.info(`FS: Associating channel ${channelId} ${callId} userId: ${userId}`);
  if (!DISCONNECT_ON_SILENCE) {
    startAudioFork(channelId, userId);
  }
}

const handleChannelHangup = (channelId, callId) => {
  Logger.info(`FS: channel hangup ${channelId} ${callId}`);
  stopAudioFork(channelId);
}

const handleFloorChanged = (roomId, newFloorMemberId) => {
  Logger.info(`FS: floor changed ${roomId} ${newFloorMemberId}`);
}

const handleStartTalking = (channelId, userId) => {
  Logger.info(`FS: Start talking ${channelId} userId: ${userId}`);

  if (DISCONNECT_ON_SILENCE) {
    if (stopTimeouts[channelId]) {
      Logger.debug(`Cancelled stop for channelId: ${channelId}`);
      clearTimeout(stopTimeouts[channelId]);
      delete stopTimeouts[channelId];
    } else {
      startAudioFork(channelId, userId);
    }
  }
}

const handleStopTalking = (channelId, userId) => {
  Logger.info(`FS: Stop Talking ${channelId} userId: ${userId}`);

  if (DISCONNECT_ON_SILENCE) {
    if (stopTimeouts[channelId]) {
      Logger.debug(`Already stopping for channelId=${channelId}`);
      return;
    };

    stopTimeouts[channelId] = setTimeout(() => {
      Logger.debug(`Actually stopping audio fork for channelId=${channelId}`);
      stopAudioFork(channelId);
      delete stopTimeouts[channelId];
    }, CLOSE_CONNECTION_AFTER_SECONDS * 1000);
  }
} 

eslWrapper.on(EslWrapper.EVENTS.CHANNEL_ANSWER, handleChannelAnswer);
eslWrapper.on(EslWrapper.EVENTS.CHANNEL_HANGUP, handleChannelHangup);
eslWrapper.on(EslWrapper.EVENTS.FLOOR_CHANGED, handleFloorChanged);
eslWrapper.on(EslWrapper.EVENTS.START_TALKING, handleStartTalking);
eslWrapper.on(EslWrapper.EVENTS.STOP_TALKING, handleStopTalking);
eslWrapper.on(EslWrapper.EVENTS.MUTED, handleStopTalking);

eslWrapper._connect();

const exitCleanup = () => {
  Logger.info('Closing process, cleaning up.');

  if (GLADIA_PROXY_PROCESS) {
    Logger.info('Killing gladia proxy');
    GLADIA_PROXY_PROCESS.kill('SIGINT');
  }
  setTimeout(() => process.exit(), 1000);
}

process.on('SIGINT', exitCleanup);
process.on('SIGQUIT', exitCleanup);
process.on('SIGTERM', exitCleanup);


4) package.json
在原来package.json 新增：

"dependencies": {
    "axios": "^v1.8.4" 
  }

5) 集成到转写控制器，集成到 bbb docker-compose.tmpl.yml

.env 新增是否开启转写配置开关
vim .env
ENABLE_TRANSCRIPTION=true
vim ./scripts/generate-compose ， 将ENABLE_TRANSCRIPTION配置成环境变量
docker run  \
    --rm  \
    -v $(pwd)/docker-compose.tmpl.yml:/docker-compose.tmpl.yml \
    -e TAG_BBB=$(get_tag repos/bigbluebutton) \
    -e TAG_FREESWITCH=$(get_tag repos/freeswitch) \
    -e TAG_WEBRTC_SFU=$(get_tag repos/bbb-webrtc-sfu) \
    -e TAG_WEBHOOKS=$(get_tag repos/bbb-webhooks) \
    -e TAG_PLAYBACK=$(get_tag repos/bbb-playback) \
    -e TAG_WEBRTC_RECORDER=$(get_tag repos/bbb-webrtc-recorder) \
    -e TAG_PADS=$(get_tag repos/bbb-pads) \
    -e COMMIT_ETHERPAD_SKIN=$(get_tag repos/bbb-etherpad-skin) \
    -e COMMIT_ETHERPAD_PLUGIN=$(get_tag repos/bbb-etherpad-plugin) \
    -e BBB_BUILD_TAG=${BBB_BUILD_TAG} \
    -e DEV_MODE=${DEV_MODE:-false} \
    -e IGNORE_TLS_CERT_ERRORS=${IGNORE_TLS_CERT_ERRORS:-} \
    -e EXTERNAL_IPv6=${EXTERNAL_IPv6:-} \
    -e SIP_IP_ALLOWLIST=${SIP_IP_ALLOWLIST:-} \
    -e ENABLE_RECORDING=${ENABLE_RECORDING:-false} \
    -e ENABLE_HTTPS_PROXY=${ENABLE_HTTPS_PROXY:-false} \
    -e ENABLE_WEBHOOKS=${ENABLE_WEBHOOKS:-false} \
    -e ENABLE_TRANSCRIPTION=${ENABLE_TRANSCRIPTION:-false} \
    -e ENABLE_GREENLIGHT=${ENABLE_GREENLIGHT:-false} \
    -e ENABLE_PROMETHEUS_EXPORTER=${ENABLE_PROMETHEUS_EXPORTER:-false} \
    -e ENABLE_PROMETHEUS_EXPORTER_OPTIMIZATION=${ENABLE_PROMETHEUS_EXPORTER_OPTIMIZATION:-false} \
    jwilder/dockerize -template /docker-compose.tmpl.yml \


vim docker-compose.tmpl.yml
外挂配置文件地址：
\bbb-docker-dev30\conf\bbb-transcription-controller\default.yml, default.yml 见2）配置文件修改
{{ if isTrue .Env.ENABLE_TRANSCRIPTION }}
  bbb-transcription-controller:
    image: bigbluebutton/transcription-controller:latest
    restart: unless-stopped
    environment:
      TZ: ${TZ}
    volumes:
      - ./conf/bbb-transcription-controller/default.yml:/app/config/default.yml
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
    networks:
      bbb-net:
        ipv4_address: *********

{{end}}

重新生成docker-compose.yml
./scripts/generate-compose

6) 编译 bbb-transcription-controller
cd bbb-transcription-controller
docker build -t bigbluebutton/transcription-controller .

7) 运行 bbb-transcription-controller
  docker-compose up -d bbb-transcription-controller

 whipser asr open开源项目 VoiceStreamAI

1) 源码下载
源码地址：git clone -b overlapping_buffers https://github.com/alesaccoia/VoiceStreamAI.git VoiceStreamAI

2) requirements.txt
websockets==12.0
speechbrain==1.0.0
pyannote.audio==3.3.1
asyncio==3.4.3
sentence-transformers
transformers
faster-whisper==1.1.1
torchvision
torch

3) Dockerfile 修改
FROM nvidia/cuda:12.3.2-cudnn9-runtime-ubuntu22.04

4) VoiceStreamAI\src\main.py 修改：新增huggingface 访问令牌
 parser.add_argument(
        "--vad-args",
        type=str,
        default='{"auth_token": "*************************************"}',
        help="JSON string of additional arguments for VAD pipeline",
    )
5)  过滤噪音数据，目前基于开源代码，从以下这两个方面做质量过滤
VoiceStreamAI\src\buffering_strategy\chunks_with_vad.py
                transcription["text"] != "" 
                and transcription["language_probability"] >= 0.675
                and transcription["language"] in ["zh", "en", "ar"]
         if (
                transcription["text"] != "" 
                and transcription["language_probability"] >= 0.675
                and transcription["language"] in ["zh", "en", "ar"]
            ):
                end = time.time()
                transcription['processing_time'] = end - start
                json_transcription = json.dumps(transcription)
                #unicode_escape_string = json_transcription.encode('utf-8').decode('unicode_escape')
                #print(f"unicode_escape_string================= '{unicode_escape_string}'")
                await websocket.send(json_transcription)
            self.client.scratch_buffer.clear()
            self.client.increment_file_counter()

6) 部署脚本-docker-compose.yaml
services:
  voicestreamai:
    container_name: voicestreamai
    image: voicestreamai:latest-cuda
    build:
      dockerfile: Dockerfile
      context: .
      platforms:
        - linux/amd64
    restart: unless-stopped
    ports:
      - 8765:8765
    environment:
      #- WHISPER__MODEL=Systran/faster-whisper-large-v3
      - HF_ENDPOINT=https://hf-mirror.com
      #- PYANNOTE_AUTH_TOKEN=*************************************
    volumes:
      #设置模型存储的外挂目录
      - /home/<USER>/faster-whisper:/root/.cache
    deploy:
      resources:
        reservations:
          devices:
            - capabilities: [gpu]


7) 编译
docker-compose build
8) 运行
docker-compose up -d

bigbluebutton-html5 开启多语种字幕
repos\bigbluebutton\bigbluebutton-html5\private\config\settings.yml
enabled: true
alwaysVisible: true
mobile: true
provider: whisper
language:
        available:
          - en-US
          - zh-CN
          - ar
    audioCaptions:
      enabled: true
      alwaysVisible: true
      # mobile: <Boolean> - controls speech transcription availability on mobile
      mobile: true
      # provider: [webspeech, vosk, gladia]
      provider: whisper
      language:
        # Available languages will depend on the transcription service
        # Google: https://cloud.google.com/speech-to-text/docs/speech-to-text-supported-languages
        # Gladia: https://docs.gladia.io/chapters/speech-to-text-api/pages/languages#supported-languages
        available:
        # - de-DE
          - en-US
          - zh-CN
          - ar
        # - hi-IN
        # - it-IT
        # - ja-JP
        #  - pt-BR
        # - ru-RU
        # - zh-CN
        # If true, automatically uses the below locale field content as transcription language
        # and the language selector in audio modal won't show up!
        forceLocale: false
        # If true, the default selected value for language selector in audio modal
        # is the below locale field content
        defaultSelectLocale: true
        # Possible Values:
        # browserLanguage: to set browser language
        # [en-US, es-ES, pt-BR,...]: to set a specific locale
        # disabled: to set disabled
        locale: disabled

重新编译
cd  /home/<USER>/bbb-docker-3.x-release
docker-compose build --parallel

docker-compose up -d

