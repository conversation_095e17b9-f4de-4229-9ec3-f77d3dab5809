#!/bin/bash

# 监控系统故障排查脚本

echo "=== 监控系统故障排查 ==="

# 检查 Docker 环境
echo "1. 检查 Docker 环境:"
if docker --version > /dev/null 2>&1; then
    echo "  ✓ Docker 可用: $(docker --version)"
    
    if docker-compose --version > /dev/null 2>&1; then
        echo "  ✓ Docker Compose 可用: $(docker-compose --version)"
    else
        echo "  ✗ Docker Compose 不可用"
    fi
else
    echo "  ✗ Docker 不可用"
    echo "  请启动 Docker Desktop"
    exit 1
fi

echo ""

# 检查配置文件
echo "2. 检查配置文件:"
config_files=(
    "docker-compose.yml"
    "loki/loki-config.yml"
    "prometheus/prometheus.yml"
    "alertmanager/alertmanager.yml"
    "grafana/grafana.ini"
)

for file in "${config_files[@]}"; do
    if [[ -f "$file" ]]; then
        echo "  ✓ $file 存在"
    else
        echo "  ✗ $file 不存在"
    fi
done

echo ""

# 检查服务状态
echo "3. 检查服务状态:"
if docker-compose ps > /dev/null 2>&1; then
    echo "  Docker Compose 项目状态:"
    docker-compose ps
else
    echo "  ✗ 无法获取服务状态"
fi

echo ""

# 检查端口占用
echo "4. 检查端口占用:"
ports=("3000" "3100" "9090" "9093" "9100")
for port in "${ports[@]}"; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        echo "  ✓ 端口 $port 已监听"
    else
        echo "  ✗ 端口 $port 未监听"
    fi
done

echo ""

# 检查服务连通性
echo "5. 检查服务连通性:"
services=("grafana:3000" "loki:3100" "prometheus:9090" "alertmanager:9093" "node-exporter:9100")

for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if curl -s --connect-timeout 3 http://10.248.17.10:$port > /dev/null; then
        echo "  ✓ $service_name ($port) 可访问"
    else
        echo "  ✗ $service_name ($port) 不可访问"
    fi
done

echo ""

# 检查日志中的错误
echo "6. 检查服务日志错误:"
service_names=("loki" "prometheus" "grafana" "alertmanager" "node-exporter")

for service in "${service_names[@]}"; do
    echo "  检查 $service 日志:"
    if docker-compose logs --tail=5 $service 2>/dev/null | grep -i error; then
        echo "    发现错误日志"
    else
        echo "    ✓ 无明显错误"
    fi
done

echo ""

# 提供修复建议
echo "7. 修复建议:"
echo "  如果发现问题，可以尝试:"
echo "  - 重启特定服务: docker-compose restart [service-name]"
echo "  - 查看详细日志: docker-compose logs [service-name]"
echo "  - 重新启动所有服务: ./start-monitoring.sh"
echo "  - 修复 Loki 配置: ./fix-loki.sh"
echo "  - 修复 AlertManager 配置: ./fix-alertmanager.sh"

echo ""

# 显示访问地址
echo "8. 服务访问地址:"
echo "  Grafana:      http://10.248.17.10:3000 (admin/1qaz!QAZ)"
echo "  Prometheus:   http://10.248.17.10:9090"
echo "  AlertManager: http://10.248.17.10:9093"
echo "  Loki:         http://10.248.17.10:3100"

echo ""
echo "=== 故障排查完成 ==="
