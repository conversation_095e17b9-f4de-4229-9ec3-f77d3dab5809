#!/bin/bash

# 启动统一监控服务脚本

echo "=== 启动统一监控栈 ==="
echo "组件: Prometheus + Loki + Grafana + AlertManager + Node Exporter"

# 检查配置文件
echo "检查配置文件..."
required_files=(
    "docker-compose.yml"
    "loki/loki-config.yml"
    "grafana/grafana.ini"
    "grafana/provisioning/datasources/datasource.yml"
    "prometheus/prometheus.yml"
    "prometheus/alert_rules.yml"
    "alertmanager/alertmanager.yml"
)

for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo "错误: 配置文件 $file 不存在"
        exit 1
    fi
done

echo "配置文件检查通过"

# 停止可能存在的旧服务
echo "停止旧服务..."
docker-compose down

# 清理旧的容器和网络
echo "清理环境..."
docker system prune -f

# 启动服务
echo "启动监控服务..."
docker-compose up -d

# 检查服务启动情况
echo "检查服务启动状态..."
sleep 10
docker-compose ps

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 验证各个服务
services=("prometheus:9090" "loki:3100" "grafana:3000" "alertmanager:9093" "node-exporter:9100")

for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)

    echo "验证 $service_name 服务..."
    service_ok=false
    for i in {1..10}; do
        if curl -s http://10.248.17.10:$port > /dev/null 2>&1; then
            echo "✓ $service_name 服务正常"
            service_ok=true
            break
        else
            echo "等待 $service_name 启动... ($i/10)"
            sleep 5
        fi
    done

    # 如果服务启动失败，显示日志
    if [[ "$service_ok" == "false" ]]; then
        echo "✗ $service_name 服务启动失败，查看日志:"
        docker-compose logs --tail=10 $service_name
        echo ""
    fi
done

# 显示服务信息
echo ""
echo "=========================================="
echo "=== 统一监控栈启动完成 ==="
echo "=========================================="
echo "服务访问地址:"
echo "  Grafana (可视化):     http://10.248.17.10:3000"
echo "  Prometheus (指标):    http://10.248.17.10:9090"
echo "  AlertManager (告警):  http://10.248.17.10:9093"
echo "  Loki (日志):         http://10.248.17.10:3100"
echo "  Node Exporter:       http://10.248.17.10:9100"
echo ""
echo "登录信息:"
echo "  用户名: admin"
echo "  密码: 1qaz!QAZ"
echo ""
echo "常用命令:"
echo "  查看所有服务: docker-compose ps"
echo "  查看日志: docker-compose logs -f [service-name]"
echo "  重启服务: docker-compose restart [service-name]"
echo "  停止服务: docker-compose down"
echo ""
echo "下一步:"
echo "  1. 在目标服务器上部署监控代理"
echo "  2. 配置 AlertManager 邮件通知"
echo "  3. 导入监控面板"
