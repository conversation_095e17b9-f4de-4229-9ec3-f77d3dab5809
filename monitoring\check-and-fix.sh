#!/bin/bash

# 完整的监控系统检查和修复脚本

echo "=== 监控系统完整检查和修复 ==="

# 1. 检查 Docker 状态
echo "1. 检查 Docker 状态:"
if docker --version > /dev/null 2>&1; then
    echo "  ✓ Docker 已安装: $(docker --version)"
    
    if docker ps > /dev/null 2>&1; then
        echo "  ✓ Docker 服务正在运行"
        DOCKER_RUNNING=true
    else
        echo "  ✗ Docker 服务未运行"
        echo "  请启动 Docker Desktop"
        DOCKER_RUNNING=false
    fi
else
    echo "  ✗ Docker 未安装"
    exit 1
fi

echo ""

# 2. 检查配置文件
echo "2. 检查配置文件状态:"

# Loki 配置
if [[ -f "loki/loki-config.yml" ]]; then
    echo "  ✓ Loki 配置文件存在"
    if grep -q "scheduler_address" loki/loki-config.yml 2>/dev/null; then
        echo "  ⚠ Loki 配置包含不兼容的字段"
    else
        echo "  ✓ Loki 配置已修复"
    fi
else
    echo "  ✗ Loki 配置文件不存在"
fi

# AlertManager 配置
if [[ -f "alertmanager/alertmanager.yml" ]]; then
    echo "  ✓ AlertManager 配置文件存在"
    # 检查文件大小和基本内容
    if [[ -s "alertmanager/alertmanager.yml" ]] && grep -q "global:" alertmanager/alertmanager.yml 2>/dev/null; then
        echo "  ✓ AlertManager 配置内容正常"
    else
        echo "  ⚠ AlertManager 配置可能有问题"
    fi
else
    echo "  ✗ AlertManager 配置文件不存在"
fi

# Prometheus 配置
if [[ -f "prometheus/prometheus.yml" ]]; then
    echo "  ✓ Prometheus 配置文件存在"
else
    echo "  ✗ Prometheus 配置文件不存在"
fi

echo ""

# 3. 如果 Docker 运行，检查服务状态
if [[ "$DOCKER_RUNNING" == "true" ]]; then
    echo "3. 检查服务状态:"
    
    if docker-compose ps > /dev/null 2>&1; then
        echo "  Docker Compose 项目状态:"
        docker-compose ps
        
        echo ""
        echo "  检查服务健康状态:"
        
        # 检查各个服务
        services=("grafana:3000" "loki:3100" "prometheus:9090" "alertmanager:9093")
        
        for service in "${services[@]}"; do
            service_name=$(echo $service | cut -d: -f1)
            port=$(echo $service | cut -d: -f2)
            
            if curl -s --connect-timeout 3 http://10.248.17.10:$port > /dev/null 2>&1; then
                echo "    ✓ $service_name ($port) 可访问"
            else
                echo "    ✗ $service_name ($port) 不可访问"
            fi
        done
    else
        echo "  ⚠ 无法获取 Docker Compose 状态"
    fi
else
    echo "3. Docker 未运行，跳过服务检查"
fi

echo ""

# 4. 提供修复建议
echo "4. 修复建议:"

if [[ "$DOCKER_RUNNING" == "false" ]]; then
    echo "  🔧 启动 Docker Desktop"
    echo "  🔧 然后运行: ./start-monitoring.sh"
else
    echo "  🔧 重启所有服务: ./start-monitoring.sh"
    echo "  🔧 修复 Loki: ./fix-loki-scheduler.sh"
    echo "  🔧 修复 AlertManager: ./fix-alertmanager.sh"
    echo "  🔧 故障排查: ./troubleshoot.sh"
fi

echo ""

# 5. 显示访问地址
echo "5. 服务访问地址:"
echo "  Grafana:      http://10.248.17.10:3000 (admin/1qaz!QAZ)"
echo "  Prometheus:   http://10.248.17.10:9090"
echo "  AlertManager: http://10.248.17.10:9093"
echo "  Loki:         http://10.248.17.10:3100"

echo ""
echo "=== 检查完成 ==="

# 6. 如果 Docker 运行且有问题，提供快速修复选项
if [[ "$DOCKER_RUNNING" == "true" ]]; then
    echo ""
    read -p "是否要重启监控服务? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "重启监控服务..."
        ./start-monitoring.sh
    fi
fi
