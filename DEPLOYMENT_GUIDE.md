# 统一监控系统部署指南

## 概述
整合了 Prometheus + Loki + Grafana + AlertManager 的完整监控栈，支持指标监控和日志收集。

## 架构
- **中心服务器**: ************ (Prometheus + Loki + Grafana + AlertManager)
- **目标服务器**: 13台服务器 (Node Exporter + Promtail)

## 端口配置
- **10.248.17.x**: Node Exporter 使用 9100 端口
- **10.248.18.x**: Node Exporter 使用 9110 端口
- **Promtail**: 所有服务器使用 9080 端口

## 快速部署步骤

### 1. 在中心服务器 (************) 部署监控栈

```bash
# 进入监控目录
cd /opt/monitoring-stack/monitoring

# 启动所有监控服务
./start-monitoring.sh
```

### 2. 批量部署监控代理到所有目标服务器

```bash
# 在中心服务器执行批量部署
cd /opt/monitoring-stack
./deploy-all-servers.sh
```

### 3. 验证部署结果

```bash
# 验证整个监控系统
./verify-monitoring.sh
```

## 服务访问地址

- **Grafana**: http://************:3000 (admin/1qaz!QAZ)
- **Prometheus**: http://************:9090
- **AlertManager**: http://************:9093
- **Loki**: http://************:3100

## 目标服务器列表

| 服务器IP | Node Exporter端口 | 状态 |
|----------|------------------|------|
| *********** | 9100 | - |
| *********** | 9100 | - |
| *********** | 9100 | - |
| ************ | 9110 | - |
| ************ | 9110 | - |
| ************ | 9110 | - |
| ************ | 9110 | - |
| ************ | 9110 | - |
| *********** | 9110 | - |
| *********** | 9110 | - |
| *********** | 9110 | - |
| *********** | 9110 | - |
| ************ | 9110 | - |

## 告警配置

已配置邮件告警：
- **SMTP服务器**: smtp.exmail.qq.com:465
- **发送邮箱**: <EMAIL>
- **接收邮箱**: <EMAIL>, <EMAIL>

## 监控功能

### 指标监控 (Prometheus)
- CPU、内存、磁盘、网络使用率
- 系统负载和进程监控
- 容器资源监控
- 自定义应用指标

### 日志监控 (Loki)
- Docker 容器日志
- 系统日志 (/var/log/*)
- Nginx 访问和错误日志
- 应用程序日志

### 告警规则
- CPU 使用率 > 80%
- 内存使用率 > 85%
- 磁盘使用率 > 85%
- 服务下线告警
- 网络异常告警

## 常用查询

### Prometheus 查询
```promql
# CPU 使用率
100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)

# 内存使用率
(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100

# 磁盘使用率
(1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100
```

### Loki 查询
```logql
# 查看特定服务器日志
{host="************"}

# 查看错误日志
{level="ERROR"}

# 查看容器日志
{job="docker"}
```

## 故障排查

### 常见问题
1. **服务无法启动**: 检查端口占用和配置文件
2. **无法连接目标服务器**: 检查SSH密码和网络连通性
3. **没有监控数据**: 检查防火墙和服务状态
4. **告警不工作**: 检查邮件配置和SMTP设置

### 检查命令
```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs [service-name]

# 测试网络连通性
curl http://************:9090/targets

# 检查告警规则
curl http://************:9090/api/v1/rules
```

## 维护操作

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart prometheus
```

### 更新配置
```bash
# 重新加载 Prometheus 配置
curl -X POST http://************:9090/-/reload

# 重启 Grafana
docker-compose restart grafana
```

### 数据备份
```bash
# 备份 Prometheus 数据
cp -r /home/<USER>/prometheus-data /backup/

# 备份 Grafana 数据
cp -r /home/<USER>/grafana-data /backup/
```
