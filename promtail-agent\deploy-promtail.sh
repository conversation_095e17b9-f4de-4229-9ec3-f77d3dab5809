#!/bin/bash

# Promtail 部署脚本 - 在目标服务器上运行

HOST_IP=$(hostname -I | awk '{print $1}')
HOSTNAME=$(hostname)

echo "=== 在主机 $HOSTNAME ($HOST_IP) 上部署 Promtail ==="

# 检查 Docker 环境
echo "检查 Docker 环境..."
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 设置环境变量
echo "设置环境变量..."
cat > .env << EOF
HOSTNAME=$HOSTNAME
HOST_IP=$HOST_IP
EOF

echo "主机信息:"
echo "  主机名: $HOSTNAME"
echo "  IP地址: $HOST_IP"

# 测试到中心服务器的连接
echo "测试到中心监控服务器的连接..."
if curl -s --connect-timeout 5 http://************:3100/ready > /dev/null; then
    echo "✓ 连接到 Loki 服务正常"
else
    echo "✗ 无法连接到 Loki 服务 (************:3100)"
    echo "请检查:"
    echo "  1. 网络连通性: ping ************"
    echo "  2. 防火墙设置"
    echo "  3. Loki 服务是否正常运行"
    read -p "是否继续部署? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 停止可能存在的旧服务
echo "停止旧的 Promtail 服务..."
docker-compose down 2>/dev/null || true

# 清理旧容器
echo "清理环境..."
docker container prune -f

# 启动 Promtail
echo "启动 Promtail 服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 验证 Promtail 服务
echo "验证 Promtail 服务..."
for i in {1..5}; do
    if curl -s http://localhost:9080/ready > /dev/null; then
        echo "✓ Promtail 服务正常"
        break
    else
        echo "等待 Promtail 启动... ($i/5)"
        sleep 3
    fi
done

# 检查日志
echo "检查 Promtail 日志..."
docker-compose logs --tail=20 promtail

echo ""
echo "=== Promtail 部署完成 ==="
echo "服务状态检查:"
echo "  本地状态: http://localhost:9080/ready"
echo "  日志查看: docker-compose logs -f promtail"
echo "  重启服务: docker-compose restart promtail"
echo ""
echo "如果有问题，请检查:"
echo "  1. 网络连接到 ************:3100"
echo "  2. Docker 容器日志权限"
echo "  3. 配置文件语法"
