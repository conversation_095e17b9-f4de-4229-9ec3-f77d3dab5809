#!/bin/bash

# Node Exporter 部署脚本 - 在目标服务器上运行

HOST_IP=$(hostname -I | awk '{print $1}')
HOSTNAME=$(hostname)

echo "=== 在主机 $HOSTNAME ($HOST_IP) 上部署 Node Exporter ==="

# 检查 Docker 环境
echo "检查 Docker 环境..."
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 检查端口占用
echo "检查端口 9100 占用情况..."
if netstat -tuln | grep -q ":9100 "; then
    echo "警告: 端口 9100 已被占用，将尝试停止现有服务"
    docker-compose down 2>/dev/null || true
fi

# 停止可能存在的旧服务
echo "停止旧的 Node Exporter 服务..."
docker stop node-exporter 2>/dev/null || true
docker rm node-exporter 2>/dev/null || true

# 启动 Node Exporter
echo "启动 Node Exporter 服务..."
docker-compose up -d

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

# 验证 Node Exporter 服务
echo "验证 Node Exporter 服务..."
for i in {1..5}; do
    if curl -s http://localhost:9100/metrics > /dev/null; then
        echo "✓ Node Exporter 服务正常"
        break
    else
        echo "等待 Node Exporter 启动... ($i/5)"
        sleep 3
    fi
done

# 显示指标样例
echo "指标样例:"
curl -s http://localhost:9100/metrics | head -10

echo ""
echo "=== Node Exporter 部署完成 ==="
echo "服务状态检查:"
echo "  本地指标: http://localhost:9100/metrics"
echo "  服务状态: docker-compose ps"
echo "  查看日志: docker-compose logs -f node-exporter"
echo "  重启服务: docker-compose restart node-exporter"
echo ""
echo "Prometheus 将从 http://$HOST_IP:9100/metrics 收集指标"
