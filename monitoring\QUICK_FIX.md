# 监控系统快速修复指南

## 🚨 Loki 配置错误修复

### 问题描述
```
failed parsing config: /etc/loki/local-config.yaml: yaml: unmarshal errors:
line 70: field stream_retention not found in type loki.ConfigWrapper
```

### ✅ 解决方案

1. **已修复的问题**：
   - 移除了不支持的 `stream_retention` 配置
   - 保留通过 `limits_config.retention_period` 统一控制

2. **快速修复命令**：
   ```bash
   cd monitoring
   chmod +x *.sh
   ./fix-loki.sh
   ```

3. **如果问题仍然存在**：
   ```bash
   # 重启 Loki 服务
   docker-compose restart loki
   
   # 查看 Loki 日志
   docker-compose logs loki
   
   # 完全重启监控栈
   ./start-monitoring.sh
   ```

## 🔧 常见问题修复

### 1. Docker Desktop 未运行
```bash
# 错误信息: cannot find the file specified
# 解决方案: 启动 Docker Desktop
```

### 2. AlertManager 配置错误
```bash
cd monitoring
./fix-alertmanager.sh
```

### 3. 端口被占用
```bash
# 检查端口占用
netstat -tuln | grep -E "(3000|3100|9090|9093|9100)"

# 停止服务
docker-compose down

# 重新启动
./start-monitoring.sh
```

### 4. 服务无法访问
```bash
# 运行故障排查
./troubleshoot.sh

# 检查防火墙
# Windows: 检查 Windows 防火墙设置
# Linux: sudo ufw status
```

## 📋 完整重启流程

如果遇到任何问题，按以下顺序执行：

```bash
# 1. 进入监控目录
cd monitoring

# 2. 设置脚本权限
chmod +x *.sh

# 3. 停止所有服务
docker-compose down

# 4. 清理环境
docker system prune -f

# 5. 重新启动
./start-monitoring.sh

# 6. 如果有问题，运行故障排查
./troubleshoot.sh
```

## 🎯 验证服务正常

### 检查服务状态
```bash
docker-compose ps
```

### 测试服务连通性
```bash
curl http://10.248.17.10:3000/api/health  # Grafana
curl http://10.248.17.10:3100/ready       # Loki
curl http://10.248.17.10:9090/-/healthy   # Prometheus
curl http://10.248.17.10:9093/api/v1/status # AlertManager
```

### 访问 Web 界面
- **Grafana**: http://10.248.17.10:3000 (admin/1qaz!QAZ)
- **Prometheus**: http://10.248.17.10:9090
- **AlertManager**: http://10.248.17.10:9093

## 📧 测试邮件告警

1. **访问 AlertManager**: http://10.248.17.10:9093
2. **在 Prometheus 中创建测试告警**
3. **检查邮箱**: <EMAIL>, <EMAIL>

## 🆘 如果仍有问题

1. **查看详细日志**：
   ```bash
   docker-compose logs [service-name]
   ```

2. **检查配置文件语法**：
   ```bash
   # Loki 配置检查
   docker run --rm -v $(pwd)/loki:/etc/loki grafana/loki:latest -config.file=/etc/loki/loki-config.yml -verify-config
   
   # AlertManager 配置检查
   docker run --rm -v $(pwd)/alertmanager:/etc/alertmanager prom/alertmanager:latest amtool config check /etc/alertmanager/alertmanager.yml
   ```

3. **重置到干净状态**：
   ```bash
   docker-compose down -v  # 删除数据卷
   docker system prune -a  # 清理所有未使用的资源
   ./start-monitoring.sh
   ```

## 📞 联系支持

如果问题仍然存在，请提供：
- 错误日志：`docker-compose logs`
- 系统信息：`docker --version`
- 配置文件内容
- 故障排查结果：`./troubleshoot.sh`
