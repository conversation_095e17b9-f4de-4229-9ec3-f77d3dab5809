#!/bin/bash

# 修复 Promtail 日志时间戳和流数量问题

echo "=== 修复 Promtail 日志问题 ==="

# 目标服务器列表
SERVERS=(
    "***********"
    "***********"
    "***********"
    "************"
    "************"
    "************"
    "************"
    "************"
    "***********"
    "***********"
    "***********"
    "***********"
    "************"
)

# SSH 配置
SSH_USER="root"
SSH_PASSWORD="Krjx!@QWE"
SSH_OPTIONS="-o StrictHostKeyChecking=no -o ConnectTimeout=10"

echo "问题描述:"
echo "  1. Promtail 发送过旧的日志条目 (7天前)"
echo "  2. 活跃日志流数量超过限制"
echo ""
echo "解决方案:"
echo "  1. 配置 Promtail 只发送最近7天的日志"
echo "  2. 减少标签数量以降低流数量"
echo ""

# 检查 sshpass 工具
if ! command -v sshpass &> /dev/null; then
    echo "错误: sshpass 未安装，正在尝试安装..."
    if command -v yum &> /dev/null; then
        sudo yum install -y sshpass
    elif command -v apt-get &> /dev/null; then
        sudo apt-get update && sudo apt-get install -y sshpass
    else
        echo "请手动安装 sshpass"
        exit 1
    fi
fi

# 检查必要目录
if [[ ! -d "promtail-agent" ]]; then
    echo "错误: promtail-agent 目录不存在"
    exit 1
fi

echo "✓ 已修复的 Promtail 配置:"
echo "  - 添加了7天时间过滤器"
echo "  - 减少了标签数量"
echo "  - 优化了流配置"
echo ""

# 为每台服务器更新配置
for server in "${SERVERS[@]}"; do
    echo "=========================================="
    echo "更新服务器: $server"
    echo "=========================================="
    
    # 测试 SSH 连接
    echo "测试 SSH 连接..."
    if ! sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "echo 'SSH连接正常'" 2>/dev/null; then
        echo "✗ 无法连接到 $server，跳过此服务器"
        continue
    fi
    
    echo "✓ SSH 连接正常"
    
    # 复制更新的配置文件
    echo "更新 Promtail 配置..."
    sshpass -p "$SSH_PASSWORD" scp $SSH_OPTIONS promtail-agent/promtail-config.yml $SSH_USER@$server:~/monitoring-agents/promtail/
    
    if [[ $? -ne 0 ]]; then
        echo "✗ 配置文件更新失败，跳过 $server"
        continue
    fi
    
    echo "✓ 配置文件更新完成"
    
    # 重启 Promtail 服务
    echo "重启 Promtail 服务..."
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server << 'EOF'
        cd ~/monitoring-agents/promtail
        
        # 停止 Promtail
        docker-compose down
        
        # 清理旧的位置文件（强制重新读取日志）
        sudo rm -f /tmp/positions.yaml
        
        # 重新启动 Promtail
        docker-compose up -d
        
        # 等待服务启动
        sleep 10
        
        # 检查服务状态
        if curl -s http://localhost:9080/ready > /dev/null; then
            echo "✓ Promtail 重启成功"
        else
            echo "✗ Promtail 重启失败"
            docker-compose logs --tail=10 promtail
        fi
EOF
    
    if [[ $? -eq 0 ]]; then
        echo "✓ $server 更新成功"
    else
        echo "✗ $server 更新失败"
    fi
    
    echo ""
done

echo "=========================================="
echo "批量更新完成"
echo "=========================================="

# 验证修复效果
echo "验证修复效果..."
sleep 30

echo "检查 Loki 日志中的错误..."
if docker-compose -f monitoring/docker-compose.yml logs loki 2>/dev/null | grep -E "(too old|429)" | tail -5; then
    echo "⚠ 仍有时间戳或流限制错误"
else
    echo "✓ 没有发现时间戳或流限制错误"
fi

echo ""
echo "修复总结:"
echo "  ✓ 配置 Promtail 过滤7天前的日志"
echo "  ✓ 减少标签数量降低流数量"
echo "  ✓ 清理位置文件强制重新读取"
echo "  ✓ 重启所有 Promtail 服务"
echo ""
echo "如果问题仍然存在:"
echo "  1. 检查 Loki 日志: docker-compose logs loki"
echo "  2. 检查 Promtail 日志: ssh root@server 'docker-compose logs promtail'"
echo "  3. 等待更长时间让旧日志过期"
