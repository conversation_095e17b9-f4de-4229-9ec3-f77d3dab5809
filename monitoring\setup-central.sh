#!/bin/bash

# 物理主机 10.248.17.10 监控环境准备脚本

echo "=== 开始准备中心化监控环境 ==="

# 创建目录结构
echo "创建目录结构..."
mkdir -p monitoring/{loki,promtail,grafana/provisioning/{datasources,dashboards}}
cd monitoring

echo "目录结构创建完成："
tree . || ls -la

# 检查 Docker 和 Docker Compose
echo "检查 Docker 环境..."
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

echo "Docker 环境检查通过"

# 检查端口占用
echo "检查端口占用情况..."
if netstat -tuln | grep -q ":3100 "; then
    echo "警告: 端口 3100 已被占用"
fi

if netstat -tuln | grep -q ":3000 "; then
    echo "警告: 端口 3000 已被占用"
fi

# 配置防火墙
echo "配置防火墙规则..."
if command -v firewall-cmd &> /dev/null; then
    sudo firewall-cmd --permanent --add-port=3100/tcp
    sudo firewall-cmd --permanent --add-port=3000/tcp
    sudo firewall-cmd --reload
    echo "防火墙配置完成"
elif command -v ufw &> /dev/null; then
    sudo ufw allow 3100/tcp
    sudo ufw allow 3000/tcp
    echo "UFW 防火墙配置完成"
else
    echo "请手动配置防火墙，开放端口 3000 和 3100"
fi

echo "=== 环境准备完成 ==="
echo "下一步：创建配置文件"
