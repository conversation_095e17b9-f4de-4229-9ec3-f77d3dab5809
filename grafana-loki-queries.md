# Grafana + Loki 日志查询指南

## 基础查询语法

### 1. 标签选择器
```logql
# 查看所有日志
{job=~".*"}

# 查看系统日志
{job="system"}

# 查看 Docker 容器日志
{job="docker"}

# 查看特定环境
{environment="production"}

# 组合条件
{job="system", environment="production"}
```

### 2. 日志过滤
```logql
# 包含特定关键词
{job="system"} |~ "error"

# 不区分大小写搜索
{job="system"} |~ "(?i)error|fail|exception"

# 排除特定内容
{job="system"} !~ "debug"

# 精确匹配
{job="system"} |= "connection refused"
```

### 3. 时间范围查询
```logql
# 最近1小时
{job="system"} [1h]

# 最近24小时的错误
{job="system"} |~ "(?i)error" [24h]

# 特定时间段（在 Grafana 时间选择器中设置）
{job="system"}
```

## 实用查询示例

### 系统监控查询

#### 1. 系统错误日志
```logql
{job="system"} |~ "(?i)error|fail|critical|alert"
```

#### 2. SSH 登录监控
```logql
{job="system", filename="/var/log/secure"} |~ "(?i)ssh|login|authentication"
```

#### 3. 定时任务日志
```logql
{job="system", filename="/var/log/cron"} |~ "(?i)cron"
```

#### 4. 邮件系统日志
```logql
{job="system", filename="/var/log/maillog"}
```

### 容器监控查询

#### 1. 所有容器日志
```logql
{job="docker"}
```

#### 2. Kafka 相关日志
```logql
{job="docker"} |~ "(?i)kafka"
```

#### 3. MySQL 相关日志
```logql
{job="docker"} |~ "(?i)mysql|database"
```

#### 4. 应用错误日志
```logql
{job="docker"} |~ "(?i)error|exception|stack trace"
```

### 主机监控查询

#### 1. 特定主机日志
```logql
{host="server-name"}
```

#### 2. 多主机对比
```logql
{host=~"10.248.17.*"}
```

#### 3. 主机资源相关
```logql
{job="system"} |~ "(?i)memory|disk|cpu|load"
```

## 高级查询功能

### 1. 日志统计
```logql
# 错误日志数量统计
count_over_time({job="system"} |~ "(?i)error" [1h])

# 按主机分组统计
sum by (host) (count_over_time({job="system"} [1h]))
```

### 2. 日志速率
```logql
# 每分钟日志速率
rate({job="system"} [1m])

# 错误日志速率
rate({job="system"} |~ "(?i)error" [5m])
```

### 3. 日志解析
```logql
# 解析 JSON 日志
{job="docker"} | json

# 解析特定字段
{job="docker"} | json | level="error"
```

## Grafana 面板配置建议

### 1. 日志面板设置
- **Visualization**: Logs
- **Options**:
  - Show time: ✅
  - Show labels: ✅
  - Wrap lines: ✅
  - Prettify JSON: ✅

### 2. 统计面板设置
- **Visualization**: Stat/Graph
- **Query**: 使用 count_over_time() 或 rate()
- **Time range**: 根据需要设置

### 3. 表格面板设置
- **Visualization**: Table
- **Query**: 使用聚合查询
- **Transform**: 按需要格式化数据

## 常用仪表板模板

### 1. 系统概览仪表板
- 总日志数量
- 错误日志趋势
- 主机状态
- 服务状态

### 2. 应用监控仪表板
- 应用错误日志
- 性能指标
- 用户活动
- API 调用统计

### 3. 安全监控仪表板
- 登录尝试
- 权限变更
- 异常访问
- 系统调用

## 告警配置

### 1. 错误日志告警
```logql
# 5分钟内错误日志超过10条
count_over_time({job="system"} |~ "(?i)error" [5m]) > 10
```

### 2. 服务异常告警
```logql
# 特定服务停止
absent_over_time({job="docker", container_name="important-service"} [5m])
```

## 性能优化建议

1. **使用标签过滤**: 先用标签缩小范围，再用文本过滤
2. **合理设置时间范围**: 避免查询过长时间段
3. **使用索引标签**: 优先使用 job, host 等索引标签
4. **避免复杂正则**: 简单的文本匹配性能更好

## 故障排查

### 1. 数据源连接问题
- 检查 Loki URL: http://loki:3100
- 确认网络连通性
- 查看 Loki 服务状态

### 2. 查询无结果
- 检查时间范围设置
- 确认标签名称正确
- 验证日志是否正在产生

### 3. 查询性能慢
- 缩小时间范围
- 使用更精确的标签过滤
- 避免复杂的正则表达式
