#!/bin/bash

# 修复 Loki 查询调度器连接错误

echo "=== 修复 Loki 查询调度器连接问题 ==="

echo "问题描述: Loki 配置中包含不支持的分布式查询字段"
echo "解决方案: 使用完全兼容的单节点配置"
echo ""

# 检查配置文件
if [[ ! -f "loki/loki-config.yml" ]]; then
    echo "✗ Loki 配置文件不存在"
    exit 1
fi

echo "✓ 配置文件已更新为完全兼容的单节点模式"

# 重启 Loki 服务
echo "重启 Loki 服务..."
if docker-compose restart loki 2>/dev/null; then
    echo "✓ Loki 服务重启成功"
    
    # 等待服务启动
    echo "等待 Loki 服务启动..."
    sleep 15
    
    # 检查服务状态
    echo "检查 Loki 服务状态..."
    if curl -s http://10.248.17.10:3100/ready > /dev/null; then
        echo "✓ Loki 服务正常运行"
        
        # 检查是否还有调度器错误
        echo "检查日志中的调度器错误..."
        sleep 5
        if docker-compose logs --tail=20 loki 2>/dev/null | grep -q "scheduler"; then
            echo "⚠ 仍有调度器相关日志，但这可能是正常的"
            echo "最新日志:"
            docker-compose logs --tail=10 loki
        else
            echo "✓ 没有发现调度器连接错误"
        fi
        
    else
        echo "✗ Loki 服务异常"
        echo "查看日志:"
        docker-compose logs --tail=20 loki
        exit 1
    fi
    
else
    echo "⚠ 无法重启 Loki 服务（可能是 Docker Desktop 未运行）"
fi

echo ""
echo "=== 修复完成 ==="
echo "配置更改:"
echo "  - 移除了所有分布式查询相关配置"
echo "  - 使用 127.0.0.1 作为实例地址"
echo "  - 简化了服务器配置"
echo "  - 保留了基本的单节点功能"
echo ""
echo "Loki 现在以单节点模式运行，不再尝试连接外部调度器"
echo ""
echo "服务访问地址: http://10.248.17.10:3100"
echo "健康检查: curl http://10.248.17.10:3100/ready"
