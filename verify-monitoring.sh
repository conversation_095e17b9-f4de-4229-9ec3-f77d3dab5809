#!/bin/bash

# 统一监控系统验证脚本

CENTRAL_SERVER="************"
TARGET_SERVERS=(
    "***********"
    "***********"
    "***********"
    "************"
    "************"
    "************"
    "************"
    "************"
    "***********"
    "***********"
    "***********"
    "***********"
    "************"
)

# SSH 配置
SSH_USER="root"
SSH_PASSWORD="Krjx!@QWE"
SSH_OPTIONS="-o StrictHostKeyChecking=no -o ConnectTimeout=5"

echo "=== 统一监控系统验证 ==="

# 1. 检查中心监控服务
echo "1. 检查中心监控服务:"

services=("prometheus:9090" "loki:3100" "grafana:3000" "alertmanager:9093" "node-exporter:9100")
for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)

    echo -n "  $service_name: "
    if curl -s http://$CENTRAL_SERVER:$port > /dev/null 2>&1; then
        echo "✓ 正常"
    else
        echo "✗ 异常"
    fi
done

echo ""

# 2. 检查各服务器监控代理
echo "2. 检查各服务器监控代理状态:"
for server in "${TARGET_SERVERS[@]}"; do
    echo "  $server:"

    # 检查 Node Exporter (根据服务器确定端口)
    if [[ $server =~ ^10\.248\.18\. ]]; then
        NODE_PORT="9110"
    else
        NODE_PORT="9100"
    fi

    echo -n "    Node Exporter ($NODE_PORT): "
    if sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "curl -s http://localhost:$NODE_PORT/metrics > /dev/null" 2>/dev/null; then
        echo "✓ 正常"
    else
        echo "✗ 异常"
    fi

    # 检查 Promtail
    echo -n "    Promtail: "
    if sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "curl -s http://localhost:9080/ready > /dev/null" 2>/dev/null; then
        echo "✓ 正常"
    else
        echo "✗ 异常"
    fi
done

echo ""

# 3. 检查日志流
echo "3. 检查日志数据流:"
echo "查询最近的日志条目..."

# 使用 Loki API 查询日志
response=$(curl -s "http://$CENTRAL_SERVER:3100/loki/api/v1/query_range?query={job=\"docker\"}&start=$(date -d '5 minutes ago' +%s)000000000&end=$(date +%s)000000000&limit=10")

if echo "$response" | grep -q '"status":"success"'; then
    log_count=$(echo "$response" | jq -r '.data.result | length' 2>/dev/null || echo "0")
    echo "  ✓ 成功获取到 $log_count 个日志流"
else
    echo "  ✗ 未获取到日志数据"
fi

echo ""

# 4. 生成测试日志
echo "4. 生成测试日志:"
for server in "${TARGET_SERVERS[@]}"; do
    echo "  在 $server 上生成测试日志..."
    ssh -o ConnectTimeout=5 -o StrictHostKeyChecking=no root@$server "echo 'Test log from $server at $(date)' | logger -t test-monitoring" 2>/dev/null || echo "    无法连接到 $server"
done

echo ""

# 5. 检查 Prometheus 目标
echo "5. 检查 Prometheus 监控目标:"
targets_response=$(curl -s "http://$CENTRAL_SERVER:9090/api/v1/targets" 2>/dev/null)
if [[ $? -eq 0 ]]; then
    echo "  ✓ Prometheus API 可访问"
    # 简单统计活跃目标
    active_targets=$(echo "$targets_response" | grep -o '"health":"up"' | wc -l)
    total_targets=$(echo "$targets_response" | grep -o '"health":' | wc -l)
    echo "  活跃目标: $active_targets/$total_targets"
else
    echo "  ✗ 无法访问 Prometheus API"
fi

echo ""

# 6. 提供访问信息
echo "6. 监控系统访问信息:"
echo "  Grafana (主面板):     http://$CENTRAL_SERVER:3000"
echo "  Prometheus (指标):    http://$CENTRAL_SERVER:9090"
echo "  AlertManager (告警):  http://$CENTRAL_SERVER:9093"
echo "  Loki (日志):         http://$CENTRAL_SERVER:3100"
echo ""
echo "  登录信息:"
echo "    用户名: admin"
echo "    密码: 1qaz!QAZ"
echo ""
echo "  常用 Loki 查询:"
echo "    所有容器日志: {job=\"docker\"}"
echo "    特定主机日志: {host=\"主机名\"}"
echo "    错误日志: {level=\"ERROR\"}"
echo "    系统日志: {job=\"system\"}"
echo ""
echo "  常用 Prometheus 查询:"
echo "    CPU 使用率: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)"
echo "    内存使用率: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100"
echo "    磁盘使用率: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100"
echo ""

# 7. 故障排查
echo "7. 如果有问题，请检查:"
echo "  - 网络连通性: ping 和 telnet 测试"
echo "  - 防火墙设置: 端口 3000, 3100, 9090, 9093, 9100"
echo "  - 服务状态: docker-compose ps"
echo "  - 日志输出: docker-compose logs [service-name]"
echo "  - 磁盘空间: df -h"
echo "  - 配置文件语法: 检查 YAML 格式"
echo ""

echo "=========================================="
echo "验证完成！"
echo "=========================================="
