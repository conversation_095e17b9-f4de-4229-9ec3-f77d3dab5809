#!/bin/bash

# 最终修复脚本 - 解决所有配置问题

echo "=== 监控系统最终修复 ==="

# 检查 Docker 状态
echo "1. 检查 Docker 状态..."
if ! docker --version > /dev/null 2>&1; then
    echo "✗ Docker 未安装"
    exit 1
fi

if ! docker ps > /dev/null 2>&1; then
    echo "⚠ Docker Desktop 未运行，请先启动 Docker Desktop"
    echo "启动后再运行此脚本"
    exit 1
fi

echo "✓ Docker 正在运行"

# 停止所有服务
echo ""
echo "2. 停止现有服务..."
docker-compose down

# 清理环境
echo "3. 清理环境..."
docker system prune -f

# 验证配置文件
echo ""
echo "4. 验证配置文件..."

# 检查 AlertManager 配置
if [[ -f "alertmanager/alertmanager.yml" ]]; then
    echo "✓ AlertManager 配置存在"
    if grep -q "subject:" alertmanager/alertmanager.yml; then
        echo "✓ AlertManager 配置包含必要字段"
    else
        echo "⚠ AlertManager 配置可能不完整"
    fi
else
    echo "✗ AlertManager 配置不存在"
fi

# 检查 Loki 配置
if [[ -f "loki/loki-config.yml" ]]; then
    echo "✓ Loki 配置存在"
    if grep -q "scheduler_address" loki/loki-config.yml; then
        echo "⚠ Loki 配置包含不兼容字段"
    else
        echo "✓ Loki 配置已修复"
    fi
else
    echo "✗ Loki 配置不存在"
fi

# 启动服务
echo ""
echo "5. 启动监控服务..."
docker-compose up -d

# 等待服务启动
echo "6. 等待服务启动..."
sleep 30

# 检查服务状态
echo ""
echo "7. 检查服务状态..."
docker-compose ps

echo ""
echo "8. 验证服务健康状态..."

# 检查各个服务
services=("grafana:3000" "loki:3100" "prometheus:9090" "alertmanager:9093" "node-exporter:9100")

for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    echo -n "  检查 $service_name ($port): "
    
    # 等待服务启动
    for i in {1..10}; do
        if curl -s --connect-timeout 3 http://************:$port > /dev/null 2>&1; then
            echo "✓ 正常"
            break
        else
            if [[ $i -eq 10 ]]; then
                echo "✗ 失败"
                echo "    查看日志: docker-compose logs $service_name"
            else
                sleep 3
            fi
        fi
    done
done

echo ""
echo "9. 检查服务日志中的错误..."

# 检查 AlertManager 日志
echo "  AlertManager 日志:"
if docker-compose logs alertmanager 2>/dev/null | grep -i error | tail -3; then
    echo "    发现错误日志"
else
    echo "    ✓ 无错误日志"
fi

# 检查 Loki 日志
echo "  Loki 日志:"
if docker-compose logs loki 2>/dev/null | grep -i error | tail -3; then
    echo "    发现错误日志"
else
    echo "    ✓ 无错误日志"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "服务访问地址:"
echo "  Grafana:      http://************:3000 (admin/1qaz!QAZ)"
echo "  Prometheus:   http://************:9090"
echo "  AlertManager: http://************:9093"
echo "  Loki:         http://************:3100"
echo ""
echo "如果仍有问题："
echo "  查看详细日志: docker-compose logs [service-name]"
echo "  重启特定服务: docker-compose restart [service-name]"
echo "  完全重启: docker-compose down && docker-compose up -d"
