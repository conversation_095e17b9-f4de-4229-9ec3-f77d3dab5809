#!/bin/bash

# 配置文件验证脚本

echo "=== 监控配置文件验证 ==="

# 检查 YAML 语法（如果有 python）
check_yaml() {
    local file=$1
    local name=$2
    
    if command -v python3 &> /dev/null; then
        if python3 -c "import yaml; yaml.safe_load(open('$file'))" 2>/dev/null; then
            echo "  ✓ $name YAML 语法正确"
            return 0
        else
            echo "  ✗ $name YAML 语法错误"
            return 1
        fi
    else
        echo "  ⚠ 无法验证 $name YAML 语法（需要 Python）"
        return 0
    fi
}

# 1. 检查 AlertManager 配置
echo "1. AlertManager 配置验证:"
if [[ -f "alertmanager/alertmanager.yml" ]]; then
    echo "  ✓ 配置文件存在"
    
    # 检查基本结构
    if grep -q "global:" alertmanager/alertmanager.yml && \
       grep -q "route:" alertmanager/alertmanager.yml && \
       grep -q "receivers:" alertmanager/alertmanager.yml; then
        echo "  ✓ 基本结构完整"
    else
        echo "  ✗ 基本结构不完整"
    fi
    
    # 检查邮件配置
    if grep -q "smtp_smarthost:" alertmanager/alertmanager.yml && \
       grep -q "email_configs:" alertmanager/alertmanager.yml; then
        echo "  ✓ 邮件配置存在"
    else
        echo "  ✗ 邮件配置缺失"
    fi
    
    # 检查 YAML 语法
    check_yaml "alertmanager/alertmanager.yml" "AlertManager"
    
    # 显示当前配置
    echo "  当前配置内容:"
    cat alertmanager/alertmanager.yml | sed 's/^/    /'
    
else
    echo "  ✗ 配置文件不存在"
fi

echo ""

# 2. 检查 Loki 配置
echo "2. Loki 配置验证:"
if [[ -f "loki/loki-config.yml" ]]; then
    echo "  ✓ 配置文件存在"
    
    # 检查是否包含问题字段
    if grep -q "scheduler_address\|stream_retention\|frontend:" loki/loki-config.yml; then
        echo "  ⚠ 包含可能不兼容的字段"
    else
        echo "  ✓ 没有发现不兼容字段"
    fi
    
    check_yaml "loki/loki-config.yml" "Loki"
else
    echo "  ✗ 配置文件不存在"
fi

echo ""

# 3. 检查 Prometheus 配置
echo "3. Prometheus 配置验证:"
if [[ -f "prometheus/prometheus.yml" ]]; then
    echo "  ✓ 配置文件存在"
    check_yaml "prometheus/prometheus.yml" "Prometheus"
else
    echo "  ✗ 配置文件不存在"
fi

echo ""

# 4. 检查 Docker Compose 配置
echo "4. Docker Compose 配置验证:"
if [[ -f "docker-compose.yml" ]]; then
    echo "  ✓ 配置文件存在"
    
    # 检查服务定义
    services=("prometheus" "loki" "grafana" "alertmanager" "node-exporter")
    for service in "${services[@]}"; do
        if grep -q "$service:" docker-compose.yml; then
            echo "    ✓ $service 服务已定义"
        else
            echo "    ✗ $service 服务未定义"
        fi
    done
    
    check_yaml "docker-compose.yml" "Docker Compose"
else
    echo "  ✗ 配置文件不存在"
fi

echo ""

# 5. 提供启动建议
echo "5. 启动建议:"
echo "  如果所有配置验证通过："
echo "    1. 启动 Docker Desktop"
echo "    2. 运行: ./start-monitoring.sh"
echo "    3. 检查服务: ./check-and-fix.sh"
echo ""
echo "  如果有配置错误："
echo "    1. 修复 AlertManager: ./fix-alertmanager.sh"
echo "    2. 修复 Loki: ./fix-loki-scheduler.sh"
echo "    3. 重新验证: ./validate-config.sh"

echo ""
echo "=== 验证完成 ==="
