#!/bin/bash

# 紧急修复脚本 - 解决 429 流限制问题

echo "=== 紧急修复 429 流限制问题 ==="

# 目标服务器列表
SERVERS=(
    "***********"
    "***********"
    "***********"
    "************"
    "************"
    "************"
    "************"
    "************"
    "***********"
    "***********"
    "***********"
    "***********"
    "************"
)

# SSH 配置
SSH_USER="root"
SSH_PASSWORD="Krjx!@QWE"
SSH_OPTIONS="-o StrictHostKeyChecking=no -o ConnectTimeout=10"

echo "紧急修复策略:"
echo "  1. 大幅增加 Loki 流限制 (100,000)"
echo "  2. 激进简化 Promtail 标签"
echo "  3. 临时停止部分 Promtail 实例"
echo "  4. 清理所有位置文件"
echo ""

# 首先重启中心 Loki 服务
echo "1. 重启中心 Loki 服务（应用新限制）..."
cd monitoring
docker-compose stop loki
sleep 5
docker-compose start loki
echo "等待 Loki 重启..."
sleep 30

# 检查 Loki 状态
if curl -s http://************:3100/ready > /dev/null; then
    echo "✓ Loki 重启成功，新限制已应用"
else
    echo "✗ Loki 重启失败"
    docker-compose logs --tail=10 loki
    exit 1
fi

cd ..

# 检查 sshpass 工具
if ! command -v sshpass &> /dev/null; then
    echo "错误: sshpass 未安装"
    exit 1
fi

# 先停止所有 Promtail 实例
echo ""
echo "2. 临时停止所有 Promtail 实例..."
for server in "${SERVERS[@]}"; do
    echo "停止 $server 上的 Promtail..."
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "cd ~/monitoring-agents/promtail && docker-compose down" 2>/dev/null &
done

# 等待所有停止完成
wait
echo "✓ 所有 Promtail 实例已停止"

# 等待 Loki 稳定
echo "等待 Loki 稳定..."
sleep 30

# 逐个重启 Promtail，避免同时启动造成流量冲击
echo ""
echo "3. 逐个重启 Promtail（简化配置）..."
for server in "${SERVERS[@]}"; do
    echo "=========================================="
    echo "重启服务器: $server"
    echo "=========================================="
    
    # 测试 SSH 连接
    if ! sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "echo 'SSH连接正常'" 2>/dev/null; then
        echo "✗ 无法连接到 $server，跳过"
        continue
    fi
    
    # 复制简化的配置文件
    echo "更新简化配置..."
    sshpass -p "$SSH_PASSWORD" scp $SSH_OPTIONS promtail-agent/* $SSH_USER@$server:~/monitoring-agents/promtail/
    
    # 清理并重启
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server << 'EOF'
        cd ~/monitoring-agents/promtail
        
        echo "  清理所有状态文件..."
        rm -f /tmp/positions.yaml
        rm -f positions.yaml
        rm -f .env
        
        echo "  创建环境变量文件..."
        cat > .env << ENVEOF
HOSTNAME=$(hostname)
HOST_IP=$(hostname -I | awk '{print $1}')
ENVEOF
        
        echo "  启动 Promtail..."
        docker-compose up -d
        
        echo "  等待启动..."
        sleep 10
        
        if curl -s http://localhost:9080/ready > /dev/null; then
            echo "  ✓ Promtail 启动成功"
        else
            echo "  ✗ Promtail 启动失败"
        fi
EOF
    
    echo "✓ $server 重启完成"
    
    # 每个服务器之间间隔，避免同时冲击 Loki
    echo "等待10秒再处理下一个服务器..."
    sleep 10
done

echo ""
echo "=========================================="
echo "紧急修复完成"
echo "=========================================="

# 等待系统稳定
echo "等待系统稳定..."
sleep 60

# 检查修复效果
echo "检查修复效果..."
echo ""
echo "Loki 错误日志:"
if docker-compose -f monitoring/docker-compose.yml logs loki 2>/dev/null | grep -E "(error|429)" | tail -5; then
    echo ""
    echo "如果仍有429错误，可能需要:"
    echo "  1. 进一步减少 Promtail 实例数量"
    echo "  2. 增加更多 Loki 资源"
    echo "  3. 考虑分批部署"
else
    echo "✓ 没有发现429错误，修复成功！"
fi

echo ""
echo "紧急修复总结:"
echo "  ✓ Loki 流限制增加到 100,000"
echo "  ✓ Promtail 标签大幅简化"
echo "  ✓ 逐个重启避免流量冲击"
echo "  ✓ 清理了所有状态文件"
echo ""
echo "当前配置:"
echo "  - Docker 日志: job=docker, environment=production"
echo "  - 系统日志: job=system"
echo "  - 移除了所有主机和容器特定标签"
