{"dashboard": {"id": null, "title": "Loki 日志监控仪表板", "tags": ["loki", "logs", "monitoring"], "timezone": "browser", "panels": [{"id": 1, "title": "最近1小时日志总数", "type": "stat", "targets": [{"expr": "sum(count_over_time({job=~\".*\"} [1h]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1000}, {"color": "red", "value": 5000}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "错误日志趋势", "type": "timeseries", "targets": [{"expr": "sum(rate({job=~\".*\"} |~ \"(?i)error|fail|critical\" [5m])) by (job)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "pointSize": 5}}}, "gridPos": {"h": 8, "w": 18, "x": 6, "y": 0}}, {"id": 3, "title": "主机日志分布", "type": "piechart", "targets": [{"expr": "sum by (host) (count_over_time({job=~\".*\"} [1h]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "服务活动统计", "type": "table", "targets": [{"expr": "sum by (job) (count_over_time({job=~\".*\"} [1h]))", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "实时日志流", "type": "logs", "targets": [{"expr": "{job=~\".*\"}", "refId": "A"}], "options": {"showTime": true, "showLabels": true, "wrapLogMessage": true, "prettifyLogMessage": true, "enableLogDetails": true}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 16}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 30, "version": 1}}