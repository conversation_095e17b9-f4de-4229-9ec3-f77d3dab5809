server {
    listen 80;

    # 日志配置
    access_log /var/log/nginx/multi-spa-access.log;
    error_log /var/log/nginx/multi-spa-error.log;

    # 通用安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Gzip 压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 根路径重定向到管理员应用
    location = / {
        return 301 /admin/;
    }

    # ==================== 管理员应用配置 ====================

    # 管理员应用静态资源 - 使用精确匹配优先级
    location ^~ /admin/ {
        alias /home/<USER>/admin/;
        index index.html index.htm;
        try_files $uri $uri/ @admin_fallback;

        # 静态资源缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options "nosniff";
        }

        # HTML 文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
    }

    # 管理员应用 SPA 回退
    location @admin_fallback {
        rewrite ^/admin/(.*)$ /admin/index.html last;
    }

    # ==================== 门户应用配置 ====================

    # 门户应用静态资源
    location ^~ /portal/ {
        alias /home/<USER>/portal/;
        index index.html index.htm;
        try_files $uri $uri/ @portal_fallback;

        # 静态资源缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options "nosniff";
        }

        # HTML 文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
    }

    # 门户应用 SPA 回退
    location @portal_fallback {
        rewrite ^/portal/(.*)$ /portal/index.html last;
    }

    # ==================== Portal-2 应用配置 ====================

    # Portal-2 应用静态资源 - 作为默认应用
    location / {
        root /home/<USER>/portal-2;
        index index.html index.htm;
        try_files $uri $uri/ @portal2_fallback;

        # 静态资源缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options "nosniff";
        }

        # HTML 文件不缓存
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
    }

    # Portal-2 应用 SPA 回退
    location @portal2_fallback {
        rewrite ^(.*)$ /index.html last;
    }

    # ==================== 通用配置 ====================

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
