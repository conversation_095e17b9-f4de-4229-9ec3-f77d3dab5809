groups:
  # 系统资源告警
  - name: system_alerts
    rules:
      # CPU 使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is above 85% for more than 5 minutes on {{ $labels.instance }}"

      # 磁盘空间告警
      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "High disk usage on {{ $labels.instance }}"
          description: "Disk usage is above 85% on {{ $labels.instance }} mount {{ $labels.mountpoint }}"

      # 磁盘空间严重告警
      - alert: CriticalDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 95
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "Critical disk usage on {{ $labels.instance }}"
          description: "Disk usage is above 95% on {{ $labels.instance }} mount {{ $labels.mountpoint }}"

  # 服务可用性告警
  - name: service_alerts
    rules:
      # 节点下线告警
      - alert: NodeDown
        expr: up{job=~"node-exporter.*"} == 0
        for: 1m
        labels:
          severity: critical
          service: infrastructure
        annotations:
          summary: "Node {{ $labels.instance }} is down"
          description: "Node {{ $labels.instance }} has been down for more than 1 minute"

      # Prometheus 目标下线
      - alert: PrometheusTargetDown
        expr: up == 0
        for: 5m
        labels:
          severity: warning
          service: monitoring
        annotations:
          summary: "Prometheus target {{ $labels.instance }} is down"
          description: "Prometheus target {{ $labels.instance }} in job {{ $labels.job }} has been down for more than 5 minutes"

  # 监控系统自身告警
  - name: monitoring_alerts
    rules:
      # Loki 服务告警
      - alert: LokiDown
        expr: up{job="loki"} == 0
        for: 2m
        labels:
          severity: critical
          service: logging
        annotations:
          summary: "Loki service is down"
          description: "Loki logging service has been down for more than 2 minutes"

      # Prometheus 数据摄入告警
      - alert: PrometheusDataIngestionHigh
        expr: rate(prometheus_tsdb_head_samples_appended_total[5m]) > 10000
        for: 10m
        labels:
          severity: warning
          service: monitoring
        annotations:
          summary: "High data ingestion rate in Prometheus"
          description: "Prometheus is ingesting data at a high rate: {{ $value }} samples/sec"

  # 网络和连接告警
  - name: network_alerts
    rules:
      # 网络连接数告警
      - alert: HighNetworkConnections
        expr: node_netstat_Tcp_CurrEstab > 1000
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "High number of TCP connections on {{ $labels.instance }}"
          description: "Number of established TCP connections is {{ $value }} on {{ $labels.instance }}"

      # 网络错误率告警
      - alert: HighNetworkErrors
        expr: rate(node_network_receive_errs_total[5m]) + rate(node_network_transmit_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
          service: network
        annotations:
          summary: "High network error rate on {{ $labels.instance }}"
          description: "Network error rate is {{ $value }} errors/sec on {{ $labels.instance }} interface {{ $labels.device }}"
