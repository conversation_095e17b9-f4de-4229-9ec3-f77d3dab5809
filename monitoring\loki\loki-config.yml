auth_enabled: false

server:
  http_listen_port: 3100
  grpc_listen_port: 9096

common:
  path_prefix: /loki
  storage:
    filesystem:
      chunks_directory: /loki/chunks
      rules_directory: /loki/rules
  replication_factor: 1
  ring:
    instance_addr: 127.0.0.1
    kvstore:
      store: inmemory

query_range:
  results_cache:
    cache:
      embedded_cache:
        enabled: true
        max_size_mb: 100

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

# 基础限制配置
limits_config:
  retention_period: 168h  # 7天保留
  ingestion_rate_mb: 128   # 增加摄入速率
  ingestion_burst_size_mb: 256
  max_query_series: 1000000
  max_concurrent_tail_requests: 100
  max_streams_per_user: 100000  # 大幅增加流限制
  max_line_size: 1048576  # 1MB
  max_entries_limit_per_query: 10000
  # 拒绝过旧的日志
  reject_old_samples: true
  reject_old_samples_max_age: 168h
  # 全局流限制
  max_global_streams_per_user: 200000

# 压缩和清理配置
compactor:
  working_directory: /loki/boltdb-shipper-compactor
  shared_store: filesystem
  compaction_interval: 10m
  retention_enabled: true
  retention_delete_delay: 2h
  retention_delete_worker_count: 150
