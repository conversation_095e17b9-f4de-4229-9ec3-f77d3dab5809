apiVersion: 1

datasources:
  # Prometheus 数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      alertmanagerUid: alertmanager

  # Loki 数据源
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    isDefault: false
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: prometheus
          matcherRegex: "traceID=(\\w+)"
          name: TraceID
          url: "$${__value.raw}"
      alertmanager_uid: alertmanager

  # AlertManager 数据源
  - name: AlertManager
    type: alertmanager
    access: proxy
    url: http://alertmanager:9093
    uid: alertmanager
    editable: true
    jsonData:
      implementation: prometheus
