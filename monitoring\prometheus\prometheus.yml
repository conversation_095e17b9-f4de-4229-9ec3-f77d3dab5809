global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'production'
    region: 'main'

# 告警规则文件
rule_files:
  - "alert_rules.yml"

# AlertManager 配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 监控目标配置
scrape_configs:
  # Prometheus 自监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # AlertManager 监控
  - job_name: 'alertmanager'
    static_configs:
      - targets: ['alertmanager:9093']
    scrape_interval: 5s

  # Loki 监控
  - job_name: 'loki'
    static_configs:
      - targets: ['loki:3100']
    scrape_interval: 5s

  # 本地 Node Exporter
  - job_name: 'node-exporter-local'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 5s

  # 远程服务器 Node Exporter
  - job_name: 'node-exporter-remote'
    static_configs:
      - targets: 
          - '************:9100'
          - '************:9100'
          - '***********:9100'
          - '************:9100'
          - '***********:9100'
          - '***********:9100'
          - '************:9100'
          - '***********:9100'
          - '************:9100'
          - '************:9110'
          - '************:9110'
          - '************:9110'
          - '************:9110'
          - '***********:9110'
          - '***********:9110'
          - '***********:9110'
          - '***********:9110'
          - '************:9110'
    scrape_interval: 15s
    scrape_timeout: 10s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: instance
        replacement: '${1}'
      - source_labels: [__address__]
        regex: '10\.248\.18\..*'
        target_label: datacenter
        replacement: 'dc-18'
      - source_labels: [__address__]
        regex: '10\.248\.17\..*'
        target_label: datacenter
        replacement: 'dc-17'

  # Docker 容器监控（如果有 cAdvisor）
  - job_name: 'cadvisor'
    static_configs:
      - targets:
          - '************:8080'
          - '************:8080'
          - '***********:8080'
          - '************:8080'
    scrape_interval: 30s
    scrape_timeout: 10s
    metrics_path: /metrics

  # 应用程序监控（根据实际情况调整）
  - job_name: 'application-metrics'
    static_configs:
      - targets:
          # 添加你的应用程序指标端点
          # - '************:8081'  # 应用指标端点
          # - '************:8081'
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        regex: '([^:]+):.*'
        target_label: instance
        replacement: '${1}'
