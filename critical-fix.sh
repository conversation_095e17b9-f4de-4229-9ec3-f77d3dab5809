#!/bin/bash

# 关键修复脚本 - 解决标签和时间戳问题

echo "=== 关键修复：标签和时间戳问题 ==="

# 目标服务器列表
SERVERS=(
    "***********"
    "***********"
    "***********"
    "************"
    "************"
    "************"
    "************"
    "************"
    "***********"
    "***********"
    "***********"
    "***********"
    "************"
)

# SSH 配置
SSH_USER="root"
SSH_PASSWORD="Krjx!@QWE"
SSH_OPTIONS="-o StrictHostKeyChecking=no -o ConnectTimeout=10"

echo "关键问题修复:"
echo "  1. 修复 'at least one label pair required' 错误"
echo "  2. 修复时间戳过新/过旧问题"
echo "  3. 添加严格的24小时时间窗口"
echo "  4. 确保所有流都有足够的标签"
echo ""

# 检查 sshpass 工具
if ! command -v sshpass &> /dev/null; then
    echo "错误: sshpass 未安装"
    exit 1
fi

# 先停止所有 Promtail 实例
echo "1. 停止所有 Promtail 实例..."
for server in "${SERVERS[@]}"; do
    echo "停止 $server..."
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "cd ~/monitoring-agents/promtail && docker-compose down" 2>/dev/null &
done

# 等待所有停止完成
wait
echo "✓ 所有 Promtail 实例已停止"

# 清理服务器上的旧日志文件
echo ""
echo "2. 清理服务器上的问题日志文件..."
for server in "${SERVERS[@]}"; do
    echo "清理 $server..."
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server << 'EOF' &
        # 清理可能导致时间戳问题的日志文件
        echo "  清理时间戳异常的日志文件..."
        
        # 备份并清理 maillog（经常有时间戳问题）
        if [[ -f /var/log/maillog ]]; then
            sudo cp /var/log/maillog /var/log/maillog.backup
            sudo truncate -s 0 /var/log/maillog
        fi
        
        # 清理其他可能有问题的日志
        sudo find /var/log -name "*.log" -type f -size +100M -exec truncate -s 0 {} \; 2>/dev/null || true
        
        # 同步系统时间
        sudo ntpdate -s time.nist.gov 2>/dev/null || sudo chrony sources 2>/dev/null || true
        
        echo "  $server 清理完成"
EOF
done

# 等待清理完成
wait
echo "✓ 所有服务器清理完成"

# 等待系统稳定
echo "等待系统稳定..."
sleep 30

# 逐个重启 Promtail
echo ""
echo "3. 逐个重启 Promtail（修复配置）..."
for server in "${SERVERS[@]}"; do
    echo "=========================================="
    echo "重启服务器: $server"
    echo "=========================================="
    
    # 测试 SSH 连接
    if ! sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server "echo 'SSH连接正常'" 2>/dev/null; then
        echo "✗ 无法连接到 $server，跳过"
        continue
    fi
    
    # 复制修复的配置文件
    echo "更新修复配置..."
    sshpass -p "$SSH_PASSWORD" scp $SSH_OPTIONS promtail-agent/* $SSH_USER@$server:~/monitoring-agents/promtail/
    
    # 重启服务
    sshpass -p "$SSH_PASSWORD" ssh $SSH_OPTIONS $SSH_USER@$server << 'EOF'
        cd ~/monitoring-agents/promtail
        
        echo "  清理 Promtail 状态..."
        rm -f /tmp/positions.yaml
        rm -f positions.yaml
        
        echo "  设置正确的环境变量..."
        cat > .env << ENVEOF
HOSTNAME=$(hostname)
HOST_IP=$(hostname -I | awk '{print $1}')
ENVEOF
        
        echo "  启动 Promtail..."
        docker-compose up -d
        
        echo "  等待启动..."
        sleep 15
        
        echo "  检查服务状态..."
        if curl -s http://localhost:9080/ready > /dev/null; then
            echo "  ✓ Promtail 启动成功"
            
            # 检查是否有标签错误
            sleep 10
            if docker-compose logs promtail 2>/dev/null | grep -q "at least one label pair"; then
                echo "  ⚠ 仍有标签错误"
            else
                echo "  ✓ 没有标签错误"
            fi
            
        else
            echo "  ✗ Promtail 启动失败"
            docker-compose logs --tail=5 promtail
        fi
EOF
    
    echo "✓ $server 重启完成"
    
    # 间隔启动
    echo "等待10秒..."
    sleep 10
done

echo ""
echo "=========================================="
echo "关键修复完成"
echo "=========================================="

# 等待系统稳定
echo "等待系统稳定..."
sleep 60

# 检查修复效果
echo "检查修复效果..."
echo ""
echo "Loki 错误日志:"
if docker-compose -f monitoring/docker-compose.yml logs loki 2>/dev/null | grep -E "(error|400|429)" | tail -10; then
    echo ""
    echo "如果仍有错误，检查具体问题:"
    echo "  - 标签错误: 检查 Promtail 配置中的标签定义"
    echo "  - 时间戳错误: 检查服务器时间同步"
    echo "  - 429错误: 可能需要进一步增加限制"
else
    echo "✓ 没有发现错误，修复成功！"
fi

echo ""
echo "关键修复总结:"
echo "  ✓ 确保所有流都有必要的标签 (job, environment, host)"
echo "  ✓ 添加严格的24小时时间窗口过滤"
echo "  ✓ 清理了问题日志文件"
echo "  ✓ 同步了服务器时间"
echo "  ✓ 逐个重启避免冲击"
echo ""
echo "当前标签配置:"
echo "  - Docker 日志: job=docker, environment=production, host=hostname"
echo "  - 系统日志: job=system, environment=production, host=hostname"
