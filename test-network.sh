#!/bin/bash

# 网络连通性测试脚本

CENTRAL_SERVER="10.248.17.10"
TARGET_SERVERS=(
    "10.248.18.22"
    "10.248.18.20" 
    "10.248.17.6"
    "10.248.17.50"
)

echo "=== 网络连通性测试 ==="
echo "中心服务器: $CENTRAL_SERVER"
echo "目标服务器: ${TARGET_SERVERS[*]}"
echo ""

# 测试从中心服务器到目标服务器
echo "1. 测试从中心服务器到目标服务器的连通性:"
for server in "${TARGET_SERVERS[@]}"; do
    echo -n "  $CENTRAL_SERVER -> $server: "
    if ping -c 1 -W 3 $server > /dev/null 2>&1; then
        echo "✓ 可达"
    else
        echo "✗ 不可达"
    fi
done

echo ""

# 测试端口连通性
echo "2. 测试中心服务器端口开放情况:"
ports=("3000" "3100")
for port in "${ports[@]}"; do
    echo -n "  端口 $port: "
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        echo "✓ 已监听"
    else
        echo "✗ 未监听"
    fi
done

echo ""

# 测试防火墙
echo "3. 测试防火墙配置:"
if command -v firewall-cmd &> /dev/null; then
    echo "  使用 firewalld:"
    for port in "${ports[@]}"; do
        if firewall-cmd --list-ports 2>/dev/null | grep -q "${port}/tcp"; then
            echo "    端口 $port: ✓ 已开放"
        else
            echo "    端口 $port: ✗ 未开放"
        fi
    done
elif command -v ufw &> /dev/null; then
    echo "  使用 UFW:"
    ufw status | grep -E "(3000|3100)"
else
    echo "  未检测到防火墙管理工具"
fi

echo ""

# 生成测试命令
echo "4. 手动测试命令:"
echo "  从目标服务器测试连接到中心服务器:"
for server in "${TARGET_SERVERS[@]}"; do
    echo "    在 $server 上执行:"
    echo "      curl -v http://$CENTRAL_SERVER:3100/ready"
    echo "      telnet $CENTRAL_SERVER 3100"
done

echo ""
echo "5. 如果连接失败，请检查:"
echo "  - 网络路由配置"
echo "  - 防火墙规则"
echo "  - SELinux 设置"
echo "  - Docker 网络配置"
