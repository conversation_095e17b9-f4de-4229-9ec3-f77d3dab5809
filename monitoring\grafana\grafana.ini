[analytics]
reporting_enabled = false

[security]
admin_user = admin
admin_password = 1qaz!QAZ
allow_embedding = true

[users]
allow_sign_up = false
allow_org_create = false
auto_assign_org = true
auto_assign_org_role = Viewer

[auth.anonymous]
enabled = false

[log]
mode = console
level = info

[paths]
data = /var/lib/grafana
logs = /var/log/grafana
plugins = /var/lib/grafana/plugins
provisioning = /etc/grafana/provisioning

[server]
http_addr = 0.0.0.0
http_port = 3000
domain = 10.248.17.10
root_url = http://10.248.17.10:3000

[database]
type = sqlite3
path = grafana.db

[session]
provider = file

[alerting]
enabled = true

[unified_alerting]
enabled = true

[feature_toggles]
enable = ngalert
